<template>
    <div id="kt_project_users_table_pane" class="tab-pane fade">
        <div class="card card-flush">
            <div class="card-body pt-0">
                <div class="table-responsive">
                    <table id="kt_project_users_table" class="table table-row-bordered table-row-dashed gy-4 align-middle fw-bold">
                        <thead class="fs-7 text-gray-400 text-uppercase">
                            <tr>
                                <th class="min-w-150px">Student Name</th>
                                <th class="min-w-90px">Year</th>
                                <th class="min-w-90px">Industries</th>
                                <th class="min-w-90px">Jobs</th>
                                <th class="min-w-90px">Comapnies</th>
                                <th class="min-w-90px">Status</th>
                                <th class="min-w-50px text-center">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="fs-6">
                            <tr v-if="loading">
                                <td colspan="9" class="text-center py-10">
                                    <div class="spinner-border" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                </td>
                            </tr>

                            <tr v-else-if="students.length === 0">
                                <td colspan="9" class="text-center py-10">
                                    <h4>No students found</h4>
                                    <p class="text-muted">Try adjusting your search criteria</p>
                                </td>
                            </tr>

                            <tr v-else v-for="student in students" :key="student?.id || Math.random()">
                                <td>
                                    <div class="d-flex align-items-center">

                                        <!-- Text and Icon -->
                                        <div class="d-flex flex-column justify-content-center">
                                            <div class="mb-1 text-gray-800 text-hover-primary d-flex align-items-center gap-2">
                                                {{ student.name }}<span v-if="student.is_following" class="bi bi-check pe-0 fs-4"></span>
                                            </div>
                                        </div>
                                    </div>
                                </td>

                                <td>
                                    {{ student.year }}
                                </td>
                                <td>
                                    {{ student.industriesCount }}
                                </td>
                                <td>
                                    {{ student.jobsCount }}
                                </td>
                                <td>
                                    {{ student.companiesCount }}
                                </td>
                                <td class="p-0">
                                    <p v-if="student.is_public" class="badge badge-light-info m-0 px-10 py-3 rounded-pill">Public</p>
                                    <p v-else class="badge badge-light-secondary text-gray-600 m-0 px-10 py-3 rounded-pill">Private</p>
                                </td>
                                <td class="d-flex align-items-center justify-content-center">
                                    <div class="like-heart float-right"></div>
                                    <a target="_blank" class="btn btn-sm btn-secondary px-4 pt-2 pb-2 rounded-2 d-flex align-items-center gap-2 ms-4">
                                        Action
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-chevron-compact-down" viewBox="0 0 16 16">
                                            <path fill-rule="evenodd" d="M1.553 6.776a.5.5 0 0 1 .67-.223L8 9.44l5.776-2.888a.5.5 0 1 1 .448.894l-6 3a.5.5 0 0 1-.448 0l-6-3a.5.5 0 0 1-.223-.67z" />
                                        </svg>
                                    </a>
                                </td>

                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</template>
<script lang="ts">
    import { defineComponent } from 'vue';

    interface Student {
        id?: number;
        name?: string;
        email?: string;
        year?: string | null;
        initials?: string | null;
        industriesCount?: number;
        jobsCount?: number;
        companiesCount?: number;
        is_following?: boolean;
        is_public?: boolean;
    }

    export default defineComponent({
        name: 'PipelineListView',
        props: {
            students: {
                type: Array as () => Student[],
                required: true,
                default: () => []
            },
            loading: {
                type: Boolean,
                required: true,
                default: false
            }
        },
        methods: {

        },
    })
</script>