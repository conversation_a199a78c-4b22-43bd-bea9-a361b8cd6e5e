<template>
    <div id="kt_project_users_table_pane" class="tab-pane fade">
        <div class="card card-flush">
            <div class="card-body pt-0">
                <div class="table-responsive">
                    <table id="kt_project_users_table" class="table table-row-bordered table-row-dashed gy-4 align-middle">
                        <thead class="fs-7 text-gray-400 text-uppercase">
                            <tr>
                                <th class="min-w-150px">Student Name</th>
                                <th class="min-w-90px">Year</th>
                                <th class="min-w-90px">Industries</th>
                                <th class="min-w-90px">Jobs</th>
                                <th class="min-w-90px">Comapnies</th>
                                <th class="min-w-90px">Status</th>
                                <th class="min-w-50px text-center">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="fs-6">
                            <tr v-if="loading">
                                <td colspan="9" class="text-center py-10">
                                    <div class="spinner-border" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                </td>
                            </tr>

                            <tr v-else-if="students.length === 0">
                                <td colspan="9" class="text-center py-10">
                                    <h4>No students found</h4>
                                    <p class="text-muted">Try adjusting your search criteria</p>
                                </td>
                            </tr>

                            <tr v-else v-for="student in students" :key="student?.id || Math.random()">
                                <td>
                                    <div class="d-flex align-items-center">

                                        <!-- Text and Icon -->
                                        <div class="d-flex flex-column justify-content-center">
                                            <div class="mb-1 d-flex align-items-center gap-2" :class="student.is_public ? '' : 'text-muted'">
                                                {{ student.is_public ? student.name : 'Private Student' }}<span v-if="student.is_following" class="bi bi-check pe-0 fs-4"></span>
                                            </div>
                                        </div>
                                    </div>
                                </td>

                                <td>
                                    {{ student.year }}
                                </td>
                                <td>
                                    {{ student.industriesCount }}
                                </td>
                                <td>
                                    {{ student.jobsCount }}
                                </td>
                                <td>
                                    {{ student.companiesCount }}
                                </td>
                                <td class="p-0">
                                    <p v-if="student.is_public" class="badge badge-light-info m-0 px-10 py-3 rounded-pill">Public</p>
                                    <p v-else class="badge badge-light-secondary text-gray-600 m-0 px-10 py-3 rounded-pill">Private</p>
                                </td>
                                <td class="d-flex align-items-center justify-content-center">
                                    <div class="like-heart float-right"></div>
                                    <button
                                        class="btn btn-sm btn-secondary px-4 pt-2 pb-2 rounded-2 d-flex align-items-center gap-2 ms-4"
                                        :class="student.is_public ? '' : 'disabled'"
                                        data-kt-menu-trigger="click"
                                        data-kt-menu-placement="bottom-end"
                                        data-kt-menu-flip="top-end"
                                    >
                                        Action
                                        <i class="fa-solid fa-angle-down"></i>
                                        <!-- <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-chevron-compact-down" viewBox="0 0 16 16">
                                            <path fill-rule="evenodd" d="M1.553 6.776a.5.5 0 0 1 .67-.223L8 9.44l5.776-2.888a.5.5 0 1 1 .448.894l-6 3a.5.5 0 0 1-.448 0l-6-3a.5.5 0 0 1-.223-.67z" />
                                        </svg> -->
                                    </button>
                                    <div v-if="student.is_public" class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-600 menu-state-bg-light-primary fs-7 w-175px py-4" data-kt-menu="true">
                                        <div class="menu-item px-3">
                                            <a :href="`/profiles/edit/${student.id}`" class="menu-link px-3">
                                                View Profile
                                            </a>
                                        </div>
                                        <div class="menu-item px-3">
                                            <a
                                                @click="toggleFollow(student.id!)"
                                                class="menu-link px-3"
                                                style="cursor: pointer;"
                                                :disabled="followingStates[student.id!]?.loading"
                                            >
                                                {{ student.is_following ? 'Unfollow' : 'Follow' }}
                                            </a>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</template>
<script lang="ts">
    import { defineComponent, onMounted, nextTick } from 'vue';
    import { MenuComponent } from "@/assets/ts/components";

    interface Student {
        id?: number;
        name?: string;
        email?: string;
        year?: string | null;
        initials?: string | null;
        industriesCount?: number;
        jobsCount?: number;
        companiesCount?: number;
        is_following?: boolean;
        is_public?: boolean;
    }

    export default defineComponent({
        name: 'PipelineListView',
        props: {
            students: {
                type: Array as () => Student[],
                required: true,
                default: () => []
            },
            loading: {
                type: Boolean,
                required: true,
                default: false
            }
        },
        data() {
            return {
                followingStates: {} as Record<number, { loading: boolean }>
            }
        },
        mounted() {
            // Initialize Metronic menu components after component is mounted
            nextTick(() => {
                MenuComponent.createInstances('[data-kt-menu="true"]');
            });
        },
        updated() {
            // Re-initialize menu components when data updates
            nextTick(() => {
                MenuComponent.createInstances('[data-kt-menu="true"]');
            });
        },
        methods: {
            async toggleFollow(studentId: number) {
                if (!studentId) return;

                // Set loading state
                this.followingStates[studentId] = { loading: true };

                try {
                    const response = await fetch('/employer/toggle-follow-student', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('value') || '',
                        },
                        body: JSON.stringify({
                            student_id: studentId
                        })
                    });

                    if (!response.ok) {
                        throw new Error('Failed to toggle follow status');
                    }

                    const data = await response.json();

                    // Update the student's follow status in the students array
                    const student = this.students.find(s => s.id === studentId);
                    if (student) {
                        student.is_following = data.is_following;
                    }

                } catch (error) {
                    console.error('Error toggling follow status:', error);
                    // You might want to show a toast notification here
                } finally {
                    // Remove loading state
                    this.followingStates[studentId] = { loading: false };
                }
            }
        },
    })
</script>