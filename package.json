{"private": true, "scripts": {"dev": "npm run development && npm run react", "development": "mix --mix-config webpack.mix.js", "watch": "mix watch", "watch-poll": "mix watch -- --watch-options-poll=1000", "hot": "mix watch --hot", "prod": "npm run production && npm run react:production", "production": "mix --production", "react": "mix --mix-config react.mix.js", "react:watch": "npm run react watch", "react:production": "npm run react --production", "test": "jest mappers.test.js"}, "dependencies": {"@amcharts/amcharts5": "^5.4.7", "@amcharts/amcharts5-geodata": "^5.1.2", "@fortawesome/fontawesome-free": "^6.1.2", "@gtm-support/vue-gtm": "^2.1.0", "@intercom/messenger-js-sdk": "^0.0.14", "@popperjs/core": "^2.11.6", "@tanstack/react-query": "^4.28.0", "@tanstack/react-query-devtools": "^4.28.0", "@tanstack/react-table": "^8.8.5", "@types/object-path": "^0.11.1", "@vue/cli-plugin-typescript": "^5.0.8", "@vueform/multiselect": "^2.5.6", "@vueuse/motion": "^2.0.0-beta.12", "animate.css": "^4.1.1", "apexcharts": "^3.35.5", "axios": "^1.1.3", "bootstrap": "^5.2.2", "bootstrap-icons": "^1.10.5", "classnames": "^2.3.2", "core-js": "^3.26.1", "deepmerge": "^4.2.2", "froala-wysiwyg-vue3": "^4.0.15", "http": "^0.0.1-security", "https": "^1.0.0", "iframe-resizer": "^4.3.9", "immutability-helper": "^3.1.1", "jquery": "^3.6.1", "jwt-service": "^11.0.0", "laravel-echo": "^1.14.2", "laravel-mix": "^6.0.49", "lightgallery": "^2.7.1", "line-awesome": "^1.3.0", "lodash": "^4.17.21", "mobile-device-detect": "^0.4.3", "moment": "^2.29.4", "node-vibrant": "^3.1.6", "nouislider": "^15.6.1", "object-path": "^0.11.8", "pinia": "^2.0.24", "pinia-plugin-persistedstate": "^2.4.0", "pusher-js": "^7.5.0", "react": "^18.2.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.2.0", "react-router-dom": "^6.9.0", "react-scripts": "^5.0.1", "react-select": "^5.8.0", "recharts": "^2.5.0", "scorm-again": "^2.6.0", "select2": "^4.1.0-rc.0", "socicon": "^3.0.5", "sweetalert2": "^11.4.8", "vee-validate": "^4.7.3", "vue": "^3.2.45", "vue-axios": "^3.5.2", "vue-currency-input": "^3.0.2", "vue-final-modal": "^3.4.11", "vue-fullpage.js": "^0.2.9", "vue-i18n": "^9.2.2", "vue-inline-svg": "^3.1.0", "vue-multiselect": "^3.1.0", "vue-router": "^4.1.6", "vue3-apexcharts": "^1.4.1", "vue3-carousel": "^0.2.16", "vue3-clipboard": "^1.0.0", "vue3-xlsx": "^1.1.2", "vuex": "^4.1.0", "xlsx": "^0.18.5", "yup": "^0.32.11"}, "devDependencies": {"@babel/preset-react": "^7.18.6", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.0-rc.0", "@types/array-sort": "^1.0.0", "@types/jest": "^29.5.0", "@types/react": "18.0.14", "@types/react-dom": "18.0.11", "@types/react-pdf": "^6.2.0", "@types/sass-loader": "^8.0.3", "@typescript-eslint/eslint-plugin": "^5.43.0", "@typescript-eslint/parser": "^5.43.0", "@vue/cli-plugin-eslint": "^5.0.8", "@vue/cli-plugin-router": "^5.0.8", "@vue/cli-plugin-vuex": "^5.0.8", "@vue/cli-service": "^5.0.8", "@vue/compiler-sfc": "^3.2.45", "@vue/eslint-config-typescript": "^11.0.2", "array-sort": "^1.0.0", "css-loader": "^6.7.2", "del": "^7.0.0", "eslint": "^8.27.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-vue": "^9.7.0", "jest": "^29.5.0", "laravel-mix-merge-manifest": "^2.1.0", "mini-css-extract-plugin": "^2.7.0", "prettier": "^2.7.1", "rtlcss-webpack-plugin": "^4.0.7", "sass": "^1.56.1", "sass-loader": "^13.2.0", "ts-jest": "^29.1.0", "ts-loader": "^9.4.1", "typescript": "^4.9.5", "vue-loader": "^16.2.0", "vuex-module-decorators": "^2.0.0", "webpack": "^5.75.0", "webpack-cli": "^5.0.0"}}