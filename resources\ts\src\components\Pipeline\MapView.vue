<template>
    <div id="kt_project_users_map_pane" class="tab-pane fade">
        <div class="card card-flush">
            <div class="card-body pt-0">
                <div class="row">
                    <div class="col-12">
                        <div class="d-flex justify-content-center align-items-center" v-if="loading">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </div>
                        <div v-else>
                            <!-- Map Container -->
                            <div id="australia-map" style="width: 100%; height: 500px;"></div>

                            <!-- Map Legend -->
                            <div class="mt-5">
                                <div class="d-flex justify-content-center align-items-center gap-4">
                                    <div class="d-flex align-items-center gap-2">
                                        <div class="w-20px h-20px rounded" style="background-color: #3F4254;"></div>
                                        <span class="fs-7 text-muted">No Students</span>
                                    </div>
                                    <div class="d-flex align-items-center gap-2">
                                        <div class="w-20px h-20px rounded" style="background-color: #E1F0FF;"></div>
                                        <span class="fs-7 text-muted">1-10 Students</span>
                                    </div>
                                    <div class="d-flex align-items-center gap-2">
                                        <div class="w-20px h-20px rounded" style="background-color: #009EF7;"></div>
                                        <span class="fs-7 text-muted">11-50 Students</span>
                                    </div>
                                    <div class="d-flex align-items-center gap-2">
                                        <div class="w-20px h-20px rounded" style="background-color: #1B84FF;"></div>
                                        <span class="fs-7 text-muted">51+ Students</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- State Students Modal -->
        <div class="modal fade" id="stateStudentsModal" tabindex="-1" aria-labelledby="stateStudentsModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="stateStudentsModalLabel">
                            Students in {{ selectedState?.name }}
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="d-flex justify-content-center align-items-center py-10" v-if="modalLoading">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </div>
                        <div v-else-if="stateStudents.length === 0" class="text-center py-10">
                            <h4>No students found</h4>
                            <p class="text-muted">No students from {{ selectedState?.name }} are interested in your company's content.</p>
                        </div>
                        <div v-else>
                            <!-- Students Grid -->
                            <div class="row g-6 g-xl-9">
                                <div class="col-md-6 col-xxl-4" v-for="student in stateStudents" :key="student.id">
                                    <div class="border p-7 rounded position-relative bg-white h-100">
                                        <!-- Follow Button Positioned at Top Right -->
                                        <div v-if="student.is_public" class="position-absolute top-0 end-0">
                                            <button type="button" class="btn d-flex align-items-center gap-1 pt-8 border-0" style="background-color: transparent; color: rgb(***********);" @click="toggleFollow(student.id!)" :disabled="followingStates[student.id!]?.loading">
                                                <i :class="student.is_following ? 'bi bi-check pe-0 fs-2' : 'bi bi-plus pe-0 fs-2'"></i>
                                                {{ student.is_following ? 'Following' : 'Follow' }}
                                            </button>
                                        </div>

                                        <!-- Student Info -->
                                        <div class="text-center">
                                            <div class="symbol symbol-100px symbol-circle mb-7">
                                                <div class="symbol-label fs-3 bg-light-primary text-primary">
                                                    {{ student.initials }}
                                                </div>
                                            </div>
                                            <div class="mb-4">
                                                <div class="text-gray-800 text-hover-primary mb-1 fs-4 fw-bold" :class="student.is_public ? '' : 'text-muted'">
                                                    {{ student.is_public ? student.name : 'Private Student' }}
                                                </div>
                                                <div class="text-muted fs-7">{{ student.year }}</div>
                                            </div>
                                            <div class="d-flex justify-content-center gap-2 mb-4">
                                                <span class="badge badge-light-info">{{ student.industriesCount }} Industries</span>
                                                <span class="badge badge-light-success">{{ student.jobsCount }} Jobs</span>
                                                <span class="badge badge-light-warning">{{ student.companiesCount }} Companies</span>
                                            </div>
                                            <div class="mb-4">
                                                <span v-if="student.is_public" class="badge badge-light-info px-4 py-2">Public</span>
                                                <span v-else class="badge badge-light-secondary text-gray-600 px-4 py-2">Private</span>
                                            </div>
                                            <div v-if="student.is_public" class="d-flex justify-content-center gap-2">
                                                <a :href="`/profiles/edit/${student.id}`" class="btn btn-sm btn-light-primary">
                                                    View Profile
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer d-flex justify-content-between align-items-center">
                        <div>
                            <span v-if="modalPaginationMeta.total" class="text-muted">
                                Showing {{ modalPaginationMeta.from || 0 }} to {{ modalPaginationMeta.to || 0 }} of {{ modalPaginationMeta.total }} students
                            </span>
                        </div>
                        <div class="d-flex gap-2">
                            <!-- Pagination -->
                            <nav v-if="modalPaginationMeta.last_page > 1">
                                <ul class="pagination pagination-sm mb-0">
                                    <li class="page-item" :class="{ disabled: modalPaginationMeta.current_page <= 1 }">
                                        <button class="page-link" @click="onModalPageChange(modalPaginationMeta.current_page - 1)" :disabled="modalPaginationMeta.current_page <= 1">
                                            Previous
                                        </button>
                                    </li>
                                    <li v-for="page in getVisiblePages()" :key="page" class="page-item" :class="{ active: page === modalPaginationMeta.current_page }">
                                        <button class="page-link" @click="onModalPageChange(page)">{{ page }}</button>
                                    </li>
                                    <li class="page-item" :class="{ disabled: modalPaginationMeta.current_page >= modalPaginationMeta.last_page }">
                                        <button class="page-link" @click="onModalPageChange(modalPaginationMeta.current_page + 1)" :disabled="modalPaginationMeta.current_page >= modalPaginationMeta.last_page">
                                            Next
                                        </button>
                                    </li>
                                </ul>
                            </nav>
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
    import { defineComponent, nextTick } from 'vue';

    // Static imports for amCharts as recommended
    import * as am5 from '@amcharts/amcharts5';
    import * as am5map from '@amcharts/amcharts5/map';
    import am5geodata_australiaLow from '@amcharts/amcharts5-geodata/australiaLow';
    import am5themes_Animated from '@amcharts/amcharts5/themes/Animated';

    interface Student {
        id?: number;
        name?: string;
        email?: string;
        year?: string | null;
        initials?: string | null;
        industriesCount?: number;
        jobsCount?: number;
        companiesCount?: number;
        is_following?: boolean;
        is_public?: boolean;
        location?: string;
    }

    interface StateData {
        id: string;
        name: string;
        code: string;
        studentCount: number;
    }

    export default defineComponent({
        name: 'PipelineMapView',
        props: {
            searchQuery: {
                type: String,
                required: true,
                default: ''
            },
            currentTab: {
                type: String,
                required: true,
                default: 'all-students'
            },
            filters: {
                type: Object as () => {
                    completedModule: boolean;
                    engagedContent: boolean;
                    industriesInGameplan: boolean;
                    companiesInGameplan: boolean;
                },
                required: true,
                default: () => ({
                    completedModule: false,
                    engagedContent: false,
                    industriesInGameplan: false,
                    companiesInGameplan: false
                })
            }
        },
        data() {
            return {
                stateCounts: [] as Array<{ state_code: string, state_name: string, student_count: number }>,
                loading: false,
                selectedState: null as StateData | null,
                stateStudents: [] as Student[],
                modalLoading: false,
                modalPaginationMeta: {} as any,
                modalPaginationLinks: {} as any,
                followingStates: {} as Record<number, { loading: boolean }>,
                stateData: [] as StateData[],
                visibilityObserver: null as MutationObserver | null
            }
        },
        watch: {
            stateCounts: {
                handler() {
                    this.updateMapData();
                },
                deep: true
            },
            loading: {
                handler(newVal: boolean) {
                    if (!newVal && this.isTabVisible()) {
                        if (!(this as any).root) {
                            // Initialize map if not already initialized
                            this.onTabActivated();
                        } else {
                            // Update existing map with new data
                            this.updateMapData();
                        }
                    }
                }
            },
            searchQuery: {
                handler() {
                    // Only fetch if map tab is active
                    if (this.isTabVisible()) {
                        this.fetchMapData();
                    }
                }
            },
            filters: {
                handler() {
                    // Only fetch if map tab is active
                    if (this.isTabVisible()) {
                        this.fetchMapData();
                    }
                },
                deep: true
            }
        },
        async mounted() {
            // Initialize amCharts properties on the component instance (following amCharts Vue guidelines)
            (this as any).root = null;
            (this as any).chart = null;
            (this as any).polygonSeries = null;

            // Set up observer to watch for when the tab becomes visible
            this.setupVisibilityObserver();

            // We'll fetch data only when the map tab is activated
            // Removed initial fetch here
        },

        beforeDestroy() {
            if (this.visibilityObserver) {
                this.visibilityObserver.disconnect();
            }
            if ((this as any).root) {
                (this as any).root.dispose();
            }
        },
        methods: {
            async fetchMapData() {
                this.loading = true;
                try {
                    const params = new URLSearchParams();

                    if (this.searchQuery) {
                        params.append('search', this.searchQuery);
                    }

                    if (this.currentTab === 'following') {
                        params.append('is_following', '1');
                    }

                    // Add filter parameters
                    if (this.filters.completedModule) {
                        params.append('completed_module', '1');
                    }
                    if (this.filters.engagedContent) {
                        params.append('engaged_content', '1');
                    }
                    if (this.filters.industriesInGameplan) {
                        params.append('industries_in_gameplan', '1');
                    }
                    if (this.filters.companiesInGameplan) {
                        params.append('companies_in_gameplan', '1');
                    }

                    const baseUrl = 'employer/get-student-state-counts';
                    const requestUrl = `${baseUrl}?${params.toString()}`;

                    const response = await fetch(requestUrl);
                    const data = await response.json();

                    this.stateCounts = data.state_counts || [];
                } catch (error) {
                    console.error('Error fetching map data:', error);
                    this.stateCounts = [];
                } finally {
                    this.loading = false;
                }
            },

            isTabVisible(): boolean {
                const mapElement = document.getElementById('australia-map');
                if (!mapElement) return false;

                // Check if the tab pane has Bootstrap active classes
                const tabPane = mapElement.closest('.tab-pane');
                if (!tabPane) return false;

                const hasActiveClass = tabPane.classList.contains('active');
                const hasShowClass = tabPane.classList.contains('show');
                return hasActiveClass && hasShowClass;
            },

            setupVisibilityObserver() {
                const mapElement = document.getElementById('australia-map');
                if (!mapElement) {

                    setTimeout(() => this.setupVisibilityObserver(), 100);
                    return;
                }

                // Create a MutationObserver to watch for Bootstrap tab class changes
                this.visibilityObserver = new MutationObserver((mutations) => {
                    mutations.forEach((mutation) => {
                        if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                            const target = mutation.target as HTMLElement;
                            const hasActiveClass = target.classList.contains('active');
                            const hasShowClass = target.classList.contains('show');


                            if (hasActiveClass && hasShowClass && !(this as any).root && !this.loading) {

                                this.onTabActivated();
                            }
                        }
                    });
                });

                // Observe the tab pane itself for class changes
                const tabPane = mapElement.closest('.tab-pane');
                if (tabPane) {
                    this.visibilityObserver.observe(tabPane, {
                        attributes: true,
                        attributeFilter: ['class']
                    });

                    // Also check if the tab is already active
                    if (tabPane.classList.contains('active') && tabPane.classList.contains('show') && !(this as any).root && !this.loading) {

                        this.onTabActivated();
                    }
                } else {

                }
            },

            onTabActivated() {
                if (!(this as any).root) {
                    // Initialize map if not already initialized
                    nextTick(() => {
                        this.initializeMap();
                    });
                } else {
                    // Map already exists, just resize it
                    (this as any).root.resize();
                }

                // Fetch data only when tab is activated
                this.fetchMapData();
            },

            initializeMap() {
                try {
                    // Dispose of existing root if it exists
                    if ((this as any).root) {
                        (this as any).root.dispose();
                        (this as any).root = null;
                    }

                    // Check if DOM element exists
                    const mapElement = document.getElementById('australia-map');
                    if (!mapElement) {
                        console.error('Map container element not found');
                        return;
                    }

                    // Create root element
                    (this as any).root = am5.Root.new("australia-map");

                    // Set themes
                    (this as any).root.setThemes([
                        am5themes_Animated.new((this as any).root)
                    ]);

                    // Create the map chart with simpler settings
                    (this as any).chart = (this as any).root.container.children.push(am5map.MapChart.new((this as any).root, {
                        panX: "translateX",
                        panY: "translateY",
                        projection: am5map.geoMercator()
                    }));

                    // Create main polygon series for Australia
                    (this as any).polygonSeries = (this as any).chart.series.push(am5map.MapPolygonSeries.new((this as any).root, {
                        geoJSON: am5geodata_australiaLow
                    }));

                    // Set up polygon template with template fields for data binding
                    (this as any).polygonSeries.mapPolygons.template.setAll({
                        tooltipText: "{name}: {studentCount} students",
                        stroke: am5.color("#ffffff"),
                        strokeWidth: 1,
                        // Use template field to bind fill color to data
                        templateField: "polygonSettings"
                    });

                    // Set up template fields for data binding
                    (this as any).polygonSeries.set("templateField", "polygonSettings");

                    // Make the series appear
                    (this as any).polygonSeries.appear(1000, 100);

                    // Add hover and click states
                    (this as any).polygonSeries.mapPolygons.template.states.create("hover", {
                        fill: am5.color("#009EF7")
                    });

                    (this as any).polygonSeries.mapPolygons.template.states.create("active", {
                        fill: am5.color("#1B84FF")
                    });

                    // Add click event
                    (this as any).polygonSeries.mapPolygons.template.on("click", (ev: any) => {
                        const dataItem = ev.target.dataItem;
                        const stateId = dataItem.get("id");
                        const stateName = dataItem.get("name");

                        this.onStateClick(stateId, stateName);
                    });

                    // Make the chart appear
                    (this as any).chart.appear(1000, 100);

                    // Update map data after everything is set up
                    this.updateMapData();
                } catch (error) {
                    console.error('Failed to initialize map:', error);
                }
            },

            updateMapData() {
                if (!(this as any).polygonSeries) {
                    console.log('MapView: polygonSeries not available yet');
                    return;
                }

                try {
                    console.log('MapView: Updating map data with state counts:', this.stateCounts);

                    // Process state counts data into amCharts data format
                    const mapData: any[] = [];

                    // Map Australian state codes to amCharts geodata IDs
                    const stateMapping: Record<string, string> = {
                        'NSW': 'AU-NSW',
                        'VIC': 'AU-VIC',
                        'QLD': 'AU-QLD',
                        'SA': 'AU-SA',
                        'WA': 'AU-WA',
                        'TAS': 'AU-TAS',
                        'NT': 'AU-NT',
                        'ACT': 'AU-ACT'
                    };

                    // Create data array for all states
                    Object.entries(stateMapping).forEach(([stateCode, mappedId]) => {
                        const stateCount = this.stateCounts.find(sc => sc.state_code === stateCode);
                        const studentCount = stateCount ? stateCount.student_count : 0;

                        let fillColor = "#74788d"; // Default gray - no students
                        if (studentCount > 0 && studentCount <= 5) {
                            fillColor = "#E1F0FF"; // Light blue - 1-5 students
                        } else if (studentCount > 5 && studentCount <= 15) {
                            fillColor = "#009EF7"; // Medium blue - 6-15 students
                        } else if (studentCount > 15) {
                            fillColor = "#1B84FF"; // Dark blue - 16+ students
                        }

                        mapData.push({
                            id: mappedId,
                            studentCount: studentCount,
                            polygonSettings: {
                                fill: am5.color(fillColor)
                            }
                        });

                        console.log(`MapView: Prepared data for ${stateCode} -> ${mappedId} with ${studentCount} students, color: ${fillColor}`);
                    });

                    console.log('MapView: Final map data:', mapData);

                    // Set data to the polygon series - this is the proper amCharts way
                    (this as any).polygonSeries.data.setAll(mapData);

                } catch (error) {
                    console.error('Failed to update map data:', error);
                }
            },

            async onStateClick(stateId: string, stateName: string) {
                this.selectedState = {
                    id: stateId,
                    name: stateName,
                    code: stateId,
                    studentCount: 0
                };

                // Show modal
                const modal = new (window as any).bootstrap.Modal(document.getElementById('stateStudentsModal'));
                modal.show();

                // Fetch students for this state
                await this.fetchStateStudents(stateId);
            },

            async fetchStateStudents(stateCode: string, page: number = 1) {
                this.modalLoading = true;
                try {
                    const params = new URLSearchParams();

                    // Extract state code from amCharts ID (e.g., 'AU-NSW' -> 'NSW')
                    const actualStateCode = stateCode.replace('AU-', '');
                    params.append('state', actualStateCode);
                    params.append('page', page.toString());

                    if (this.searchQuery) {
                        params.append('search', this.searchQuery);
                    }

                    if (this.currentTab === 'following') {
                        params.append('is_following', '1');
                    }

                    // Add filter parameters
                    if (this.filters.completedModule) {
                        params.append('completed_module', '1');
                    }
                    if (this.filters.engagedContent) {
                        params.append('engaged_content', '1');
                    }
                    if (this.filters.industriesInGameplan) {
                        params.append('industries_in_gameplan', '1');
                    }
                    if (this.filters.companiesInGameplan) {
                        params.append('companies_in_gameplan', '1');
                    }

                    const baseUrl = 'employer/get-students';
                    const requestUrl = `${baseUrl}?${params.toString()}`;

                    const response = await fetch(requestUrl);
                    const data = await response.json();
                    this.stateStudents = data.result.data || [];
                    this.modalPaginationMeta = data.meta || {};
                    this.modalPaginationLinks = data.links || {};
                } catch (error) {
                    console.error('Error fetching state students:', error);
                    this.stateStudents = [];
                } finally {
                    this.modalLoading = false;
                }
            },

            async toggleFollow(studentId: number) {
                if (!studentId) return;

                // Set loading state
                this.followingStates[studentId] = { loading: true };

                try {
                    const response = await fetch('/employer/toggle-follow-student', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('value') || '',
                        },
                        body: JSON.stringify({
                            student_id: studentId
                        })
                    });

                    if (!response.ok) {
                        throw new Error('Failed to toggle follow status');
                    }

                    const data = await response.json();

                    // Update the student's follow status in the students array
                    const student = this.stateStudents.find(s => s.id === studentId);
                    if (student) {
                        student.is_following = data.is_following;
                    }

                } catch (error) {
                    console.error('Error toggling follow status:', error);
                } finally {
                    // Remove loading state
                    this.followingStates[studentId] = { loading: false };
                }
            },

            onModalPageChange(page: number) {
                if (this.selectedState) {
                    this.fetchStateStudents(this.selectedState.code, page);
                }
            },

            getVisiblePages(): number[] {
                const current = this.modalPaginationMeta.current_page || 1;
                const last = this.modalPaginationMeta.last_page || 1;
                const pages: number[] = [];

                // Show max 5 pages around current page
                const start = Math.max(1, current - 2);
                const end = Math.min(last, current + 2);

                for (let i = start; i <= end; i++) {
                    pages.push(i);
                }

                return pages;
            }
        }
    });
</script>