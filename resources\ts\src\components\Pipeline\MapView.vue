<template>
    <div id="kt_project_users_map_pane" class="tab-pane fade">
        <div class="card card-flush">
            <div class="card-body pt-0">
                <div class="row">
                    <div class="col-12">
                        <div class="d-flex justify-content-center align-items-center" v-if="loading">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </div>
                        <div v-else>
                            <!-- Map Container -->
                            <div id="australia-map" style="width: 100%; height: 500px;"></div>

                            <!-- Map Legend -->
                            <div class="mt-5">
                                <div class="d-flex justify-content-center align-items-center gap-4">
                                    <div class="d-flex align-items-center gap-2">
                                        <div class="w-20px h-20px rounded" style="background-color: #3F4254;"></div>
                                        <span class="fs-7 text-muted">No Students</span>
                                    </div>
                                    <div class="d-flex align-items-center gap-2">
                                        <div class="w-20px h-20px rounded" style="background-color: #E1F0FF;"></div>
                                        <span class="fs-7 text-muted">1-10 Students</span>
                                    </div>
                                    <div class="d-flex align-items-center gap-2">
                                        <div class="w-20px h-20px rounded" style="background-color: #009EF7;"></div>
                                        <span class="fs-7 text-muted">11-50 Students</span>
                                    </div>
                                    <div class="d-flex align-items-center gap-2">
                                        <div class="w-20px h-20px rounded" style="background-color: #1B84FF;"></div>
                                        <span class="fs-7 text-muted">51+ Students</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- State Students Modal -->
        <div class="modal fade" id="stateStudentsModal" tabindex="-1" aria-labelledby="stateStudentsModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="stateStudentsModalLabel">
                            Students in {{ selectedState?.name }}
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="d-flex justify-content-center align-items-center py-10" v-if="modalLoading">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </div>
                        <div v-else-if="stateStudents.length === 0" class="text-center py-10">
                            <h4>No students found</h4>
                            <p class="text-muted">No students from {{ selectedState?.name }} are interested in your company's content.</p>
                        </div>
                        <div v-else>
                            <!-- Students Grid -->
                            <div class="row g-6 g-xl-9">
                                <div class="col-md-6 col-xxl-4" v-for="student in stateStudents" :key="student.id">
                                    <div class="border p-7 rounded position-relative bg-white h-100">
                                        <!-- Follow Button Positioned at Top Right -->
                                        <div v-if="student.is_public" class="position-absolute top-0 end-0">
                                            <button type="button" class="btn d-flex align-items-center gap-1 pt-8 border-0" style="background-color: transparent; color: rgb(***********);" @click="toggleFollow(student.id!)" :disabled="followingStates[student.id!]?.loading">
                                                <i :class="student.is_following ? 'bi bi-check pe-0 fs-2' : 'bi bi-plus pe-0 fs-2'"></i>
                                                {{ student.is_following ? 'Following' : 'Follow' }}
                                            </button>
                                        </div>

                                        <!-- Student Info -->
                                        <div class="text-center">
                                            <div class="symbol symbol-100px symbol-circle mb-7">
                                                <div class="symbol-label fs-3 bg-light-primary text-primary">
                                                    {{ student.initials }}
                                                </div>
                                            </div>
                                            <div class="mb-4">
                                                <div class="text-gray-800 text-hover-primary mb-1 fs-4 fw-bold" :class="student.is_public ? '' : 'text-muted'">
                                                    {{ student.is_public ? student.name : 'Private Student' }}
                                                </div>
                                                <div class="text-muted fs-7">{{ student.year }}</div>
                                            </div>
                                            <div class="d-flex justify-content-center gap-2 mb-4">
                                                <span class="badge badge-light-info">{{ student.industriesCount }} Industries</span>
                                                <span class="badge badge-light-success">{{ student.jobsCount }} Jobs</span>
                                                <span class="badge badge-light-warning">{{ student.companiesCount }} Companies</span>
                                            </div>
                                            <div class="mb-4">
                                                <span v-if="student.is_public" class="badge badge-light-info px-4 py-2">Public</span>
                                                <span v-else class="badge badge-light-secondary text-gray-600 px-4 py-2">Private</span>
                                            </div>
                                            <div v-if="student.is_public" class="d-flex justify-content-center gap-2">
                                                <a :href="`/profiles/edit/${student.id}`" class="btn btn-sm btn-light-primary">
                                                    View Profile
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
import { defineComponent, onMounted, onUnmounted, nextTick } from 'vue';

// Declare global variables for amCharts modules
declare const window: any;

interface Student {
    id?: number;
    name?: string;
    email?: string;
    year?: string | null;
    initials?: string | null;
    industriesCount?: number;
    jobsCount?: number;
    companiesCount?: number;
    is_following?: boolean;
    is_public?: boolean;
    location?: string;
}

interface StateData {
    id: string;
    name: string;
    code: string;
    studentCount: number;
}

export default defineComponent({
    name: 'PipelineMapView',
    props: {
        students: {
            type: Array as () => Student[],
            required: true,
            default: () => []
        },
        loading: {
            type: Boolean,
            required: true,
            default: false
        }
    },
    data() {
        return {
            root: null as any,
            chart: null as any,
            polygonSeries: null as any,
            selectedState: null as StateData | null,
            stateStudents: [] as Student[],
            modalLoading: false,
            followingStates: {} as Record<number, { loading: boolean }>,
            stateData: [] as StateData[]
        }
    },
    mounted() {
        this.initializeMap();
    },
    unmounted() {
        if (this.root) {
            this.root.dispose();
        }
    },
    watch: {
        students: {
            handler() {
                this.updateMapData();
            },
            deep: true
        }
    },
    methods: {
        loadAmChartsModule(moduleName: string) {
            return new Promise((resolve, reject) => {
                try {
                    const module = require(moduleName);
                    resolve(module.default || module);
                } catch (error) {
                    reject(error);
                }
            });
        },

        async initializeMap() {
            try {
                // Load amCharts modules dynamically using require
                const am5 = await this.loadAmChartsModule('@amcharts/amcharts5') as any;
                const am5map = await this.loadAmChartsModule('@amcharts/amcharts5/map') as any;
                const am5geodata_australiaLow = await this.loadAmChartsModule('@amcharts/amcharts5-geodata/australiaLow') as any;
                const am5themes_Animated = await this.loadAmChartsModule('@amcharts/amcharts5/themes/Animated') as any;

                // Create root element
                this.root = am5.Root.new("australia-map");

                // Set themes
                this.root.setThemes([
                    am5themes_Animated.new(this.root)
                ]);

                // Create the map chart
                this.chart = this.root.container.children.push(am5map.MapChart.new(this.root, {
                    panX: "rotateX",
                    panY: "rotateY",
                    projection: am5map.geoMercator()
                }));

                // Create main polygon series for countries
                this.polygonSeries = this.chart.series.push(am5map.MapPolygonSeries.new(this.root, {
                    geoJSON: am5geodata_australiaLow
                }));

            this.polygonSeries.mapPolygons.template.setAll({
                tooltipText: "{name}: {studentCount} students",
                toggleKey: "active",
                interactive: true,
                fill: am5.color("#3F4254")
            });

            this.polygonSeries.mapPolygons.template.states.create("hover", {
                fill: am5.color("#009EF7")
            });

            this.polygonSeries.mapPolygons.template.states.create("active", {
                fill: am5.color("#1B84FF")
            });

                // Add click event
                this.polygonSeries.mapPolygons.template.on("click", (ev: any) => {
                    const dataItem = ev.target.dataItem;
                    const stateId = dataItem.get("id");
                    const stateName = dataItem.get("name");

                    this.onStateClick(stateId, stateName);
                });

                this.updateMapData();
            } catch (error) {
                console.error('Failed to initialize map:', error);
            }
        },

        async updateMapData() {
            if (!this.polygonSeries) return;

            try {
                const am5 = await this.loadAmChartsModule('@amcharts/amcharts5') as any;

                // Process student data by state
                const stateStudentCounts: Record<string, number> = {};

                this.students.forEach(student => {
                    if (student.location) {
                        stateStudentCounts[student.location] = (stateStudentCounts[student.location] || 0) + 1;
                    }
                });

                // Update polygon colors based on student counts
                this.polygonSeries.mapPolygons.each((polygon: any) => {
                    const stateId = polygon.dataItem.get("id");
                    const studentCount = stateStudentCounts[stateId] || 0;

                    polygon.dataItem.set("studentCount", studentCount);

                    let color = "#3F4254"; // No students
                    if (studentCount > 0 && studentCount <= 10) {
                        color = "#E1F0FF"; // 1-10 students
                    } else if (studentCount > 10 && studentCount <= 50) {
                        color = "#009EF7"; // 11-50 students
                    } else if (studentCount > 50) {
                        color = "#1B84FF"; // 51+ students
                    }

                    polygon.set("fill", am5.color(color));
                });
            } catch (error) {
                console.error('Failed to update map data:', error);
            }
        },

        async onStateClick(stateId: string, stateName: string) {
            this.selectedState = {
                id: stateId,
                name: stateName,
                code: stateId,
                studentCount: 0
            };

            // Show modal
            const modal = new (window as any).bootstrap.Modal(document.getElementById('stateStudentsModal'));
            modal.show();

            // Fetch students for this state
            await this.fetchStateStudents(stateId);
        },

        async fetchStateStudents(stateCode: string) {
            this.modalLoading = true;
            try {
                const response = await fetch(`/employer/get-students?state=${stateCode}`);
                const data = await response.json();
                this.stateStudents = data.result.data || [];
            } catch (error) {
                console.error('Error fetching state students:', error);
                this.stateStudents = [];
            } finally {
                this.modalLoading = false;
            }
        },

        async toggleFollow(studentId: number) {
            if (!studentId) return;

            // Set loading state
            this.followingStates[studentId] = { loading: true };

            try {
                const response = await fetch('/employer/toggle-follow-student', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('value') || '',
                    },
                    body: JSON.stringify({
                        student_id: studentId
                    })
                });

                if (!response.ok) {
                    throw new Error('Failed to toggle follow status');
                }

                const data = await response.json();

                // Update the student's follow status in the students array
                const student = this.stateStudents.find(s => s.id === studentId);
                if (student) {
                    student.is_following = data.is_following;
                }

            } catch (error) {
                console.error('Error toggling follow status:', error);
            } finally {
                // Remove loading state
                this.followingStates[studentId] = { loading: false };
            }
        }
    }
});
</script>

<style scoped>
.w-20px {
    width: 20px !important;
}

.h-20px {
    height: 20px !important;
}
</style>
