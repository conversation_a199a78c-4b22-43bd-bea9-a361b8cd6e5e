<template>
    <div id="kt_project_users_map_pane" class="tab-pane fade">
        <div class="card card-flush">
            <div class="card-body pt-0">
                <div class="row">
                    <div class="col-12">
                        <div class="d-flex justify-content-center align-items-center" v-if="loading">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </div>
                        <div v-else>
                            <!-- Map Container -->
                            <div id="australia-map" style="width: 100%; height: 500px;"></div>

                            <!-- Map Legend -->
                            <div class="mt-5">
                                <div class="d-flex justify-content-center align-items-center gap-4">
                                    <div class="d-flex align-items-center gap-2">
                                        <div class="w-20px h-20px rounded" style="background-color: #3F4254;"></div>
                                        <span class="fs-7 text-muted">No Students</span>
                                    </div>
                                    <div class="d-flex align-items-center gap-2">
                                        <div class="w-20px h-20px rounded" style="background-color: #E1F0FF;"></div>
                                        <span class="fs-7 text-muted">1-10 Students</span>
                                    </div>
                                    <div class="d-flex align-items-center gap-2">
                                        <div class="w-20px h-20px rounded" style="background-color: #009EF7;"></div>
                                        <span class="fs-7 text-muted">11-50 Students</span>
                                    </div>
                                    <div class="d-flex align-items-center gap-2">
                                        <div class="w-20px h-20px rounded" style="background-color: #1B84FF;"></div>
                                        <span class="fs-7 text-muted">51+ Students</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- State Students Modal -->
        <div class="modal fade" id="stateStudentsModal" tabindex="-1" aria-labelledby="stateStudentsModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="stateStudentsModalLabel">
                            Students in {{ selectedState?.name }}
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="d-flex justify-content-center align-items-center py-10" v-if="modalLoading">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </div>
                        <div v-else-if="stateStudents.length === 0" class="text-center py-10">
                            <h4>No students found</h4>
                            <p class="text-muted">No students from {{ selectedState?.name }} are interested in your company's content.</p>
                        </div>
                        <div v-else>
                            <!-- Students Grid -->
                            <div class="row g-6 g-xl-9">
                                <div class="col-md-6 col-xxl-4" v-for="student in stateStudents" :key="student.id">
                                    <div class="border p-7 rounded position-relative bg-white h-100">
                                        <!-- Follow Button Positioned at Top Right -->
                                        <div v-if="student.is_public" class="position-absolute top-0 end-0">
                                            <button type="button" class="btn d-flex align-items-center gap-1 pt-8 border-0" style="background-color: transparent; color: rgb(***********);" @click="toggleFollow(student.id!)" :disabled="followingStates[student.id!]?.loading">
                                                <i :class="student.is_following ? 'bi bi-check pe-0 fs-2' : 'bi bi-plus pe-0 fs-2'"></i>
                                                {{ student.is_following ? 'Following' : 'Follow' }}
                                            </button>
                                        </div>

                                        <!-- Student Info -->
                                        <div class="text-center">
                                            <div class="symbol symbol-100px symbol-circle mb-7">
                                                <div class="symbol-label fs-3 bg-light-primary text-primary">
                                                    {{ student.initials }}
                                                </div>
                                            </div>
                                            <div class="mb-4">
                                                <div class="text-gray-800 text-hover-primary mb-1 fs-4 fw-bold" :class="student.is_public ? '' : 'text-muted'">
                                                    {{ student.is_public ? student.name : 'Private Student' }}
                                                </div>
                                                <div class="text-muted fs-7">{{ student.year }}</div>
                                            </div>
                                            <div class="d-flex justify-content-center gap-2 mb-4">
                                                <span class="badge badge-light-info">{{ student.industriesCount }} Industries</span>
                                                <span class="badge badge-light-success">{{ student.jobsCount }} Jobs</span>
                                                <span class="badge badge-light-warning">{{ student.companiesCount }} Companies</span>
                                            </div>
                                            <div class="mb-4">
                                                <span v-if="student.is_public" class="badge badge-light-info px-4 py-2">Public</span>
                                                <span v-else class="badge badge-light-secondary text-gray-600 px-4 py-2">Private</span>
                                            </div>
                                            <div v-if="student.is_public" class="d-flex justify-content-center gap-2">
                                                <a :href="`/profiles/edit/${student.id}`" class="btn btn-sm btn-light-primary">
                                                    View Profile
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
import { defineComponent, nextTick } from 'vue';

// Static imports for amCharts as recommended
import * as am5 from '@amcharts/amcharts5';
import * as am5map from '@amcharts/amcharts5/map';
import am5geodata_australiaLow from '@amcharts/amcharts5-geodata/australiaLow';
import am5themes_Animated from '@amcharts/amcharts5/themes/Animated';

interface Student {
    id?: number;
    name?: string;
    email?: string;
    year?: string | null;
    initials?: string | null;
    industriesCount?: number;
    jobsCount?: number;
    companiesCount?: number;
    is_following?: boolean;
    is_public?: boolean;
    location?: string;
}

interface StateData {
    id: string;
    name: string;
    code: string;
    studentCount: number;
}

export default defineComponent({
    name: 'PipelineMapView',
    props: {
        searchQuery: {
            type: String,
            required: true,
            default: ''
        },
        currentTab: {
            type: String,
            required: true,
            default: 'all-students'
        },
        filters: {
            type: Object as () => {
                completedModule: boolean;
                engagedContent: boolean;
                industriesInGameplan: boolean;
                companiesInGameplan: boolean;
            },
            required: true,
            default: () => ({
                completedModule: false,
                engagedContent: false,
                industriesInGameplan: false,
                companiesInGameplan: false
            })
        }
    },
    data() {
        return {
            students: [] as Student[],
            loading: false,
            selectedState: null as StateData | null,
            stateStudents: [] as Student[],
            modalLoading: false,
            followingStates: {} as Record<number, { loading: boolean }>,
            stateData: [] as StateData[],
            visibilityObserver: null as MutationObserver | null
        }
    },
    watch: {
        students: {
            handler() {
                this.updateMapData();
            },
            deep: true
        },
        loading: {
            handler(newVal: boolean) {
                if (!newVal && this.isTabVisible() && !(this as any).root) {
                    // Loading finished, map not initialized yet, and tab is visible
                    this.onTabActivated();
                }
            }
        },
        searchQuery: {
            handler() {
                this.fetchMapData();
            }
        },
        currentTab: {
            handler() {
                this.fetchMapData();
            }
        },
        filters: {
            handler() {
                this.fetchMapData();
            },
            deep: true
        }
    },
    async mounted() {
        // Initialize amCharts properties on the component instance
        (this as any).root = null;
        (this as any).chart = null;
        (this as any).polygonSeries = null;

        // Set up observer to watch for when the tab becomes visible
        this.setupVisibilityObserver();

        // Fetch initial map data
        this.fetchMapData();
    },

    beforeUnmount() {
        if (this.visibilityObserver) {
            this.visibilityObserver.disconnect();
        }
        if ((this as any).root) {
            (this as any).root.dispose();
        }
    },
    unmounted() {
        if ((this as any).root) {
            (this as any).root.dispose();
        }
    },
    methods: {
        async fetchMapData() {
            this.loading = true;
            try {
                const params = new URLSearchParams();

                if (this.searchQuery) {
                    params.append('search', this.searchQuery);
                }

                if (this.currentTab === 'following') {
                    params.append('is_following', '1');
                }

                // Add filter parameters
                if (this.filters.completedModule) {
                    params.append('completed_module', '1');
                }
                if (this.filters.engagedContent) {
                    params.append('engaged_content', '1');
                }
                if (this.filters.industriesInGameplan) {
                    params.append('industries_in_gameplan', '1');
                }
                if (this.filters.companiesInGameplan) {
                    params.append('companies_in_gameplan', '1');
                }

                // Don't use pagination for map data - get all students
                params.append('per_page', '1000');

                const baseUrl = 'employer/get-students';
                const requestUrl = `${baseUrl}?${params.toString()}`;

                const response = await fetch(requestUrl);
                const data = await response.json();

                this.students = data.result.data || [];
            } catch (error) {
                console.error('Error fetching map data:', error);
                this.students = [];
            } finally {
                this.loading = false;
            }
        },

        isTabVisible(): boolean {
            const mapElement = document.getElementById('australia-map');
            if (!mapElement) return false;

            // Check if the tab pane has Bootstrap active classes
            const tabPane = mapElement.closest('.tab-pane');
            if (!tabPane) return false;

            const hasActiveClass = tabPane.classList.contains('active');
            const hasShowClass = tabPane.classList.contains('show');
            return hasActiveClass && hasShowClass;
        },

        setupVisibilityObserver() {
            const mapElement = document.getElementById('australia-map');
            if (!mapElement) {

                setTimeout(() => this.setupVisibilityObserver(), 100);
                return;
            }

            // Create a MutationObserver to watch for Bootstrap tab class changes
            this.visibilityObserver = new MutationObserver((mutations) => {
                mutations.forEach((mutation) => {
                    if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                        const target = mutation.target as HTMLElement;
                        const hasActiveClass = target.classList.contains('active');
                        const hasShowClass = target.classList.contains('show');


                        if (hasActiveClass && hasShowClass && !(this as any).root && !this.loading) {

                            this.onTabActivated();
                        }
                    }
                });
            });

            // Observe the tab pane itself for class changes
            const tabPane = mapElement.closest('.tab-pane');
            if (tabPane) {
                this.visibilityObserver.observe(tabPane, {
                    attributes: true,
                    attributeFilter: ['class']
                });

                // Also check if the tab is already active
                if (tabPane.classList.contains('active') && tabPane.classList.contains('show') && !(this as any).root && !this.loading) {

                    this.onTabActivated();
                }
            } else {

            }
        },

        onTabActivated() {
            if (!this.loading && !(this as any).root) {
                nextTick(() => {
                    this.initializeMap();
                });
            } else if ((this as any).root) {
                // Map already exists, just resize it
                (this as any).root.resize();
            }
        },

        initializeMap() {
            try {
                // Dispose of existing root if it exists
                if ((this as any).root) {
                    (this as any).root.dispose();
                    (this as any).root = null;
                }

                // Check if DOM element exists
                const mapElement = document.getElementById('australia-map');
                if (!mapElement) {
                    console.error('Map container element not found');
                    return;
                }

                // Create root element
                (this as any).root = am5.Root.new("australia-map");

                // Set themes
                (this as any).root.setThemes([
                    am5themes_Animated.new((this as any).root)
                ]);

                // Create the map chart with simpler settings
                (this as any).chart = (this as any).root.container.children.push(am5map.MapChart.new((this as any).root, {
                    panX: "translateX",
                    panY: "translateY",
                    projection: am5map.geoMercator()
                }));

                // Create main polygon series for Australia
                (this as any).polygonSeries = (this as any).chart.series.push(am5map.MapPolygonSeries.new((this as any).root, {
                    geoJSON: am5geodata_australiaLow
                }));

                // Set up polygon template BEFORE calling appear()
                (this as any).polygonSeries.mapPolygons.template.setAll({
                    tooltipText: "{name}: {studentCount} students",
                    fill: am5.color("#74788d"),
                    stroke: am5.color("#ffffff"),
                    strokeWidth: 1
                });

                // Make the series appear
                (this as any).polygonSeries.appear(1000, 100);

                // Add hover and click states
                (this as any).polygonSeries.mapPolygons.template.states.create("hover", {
                    fill: am5.color("#009EF7")
                });

                (this as any).polygonSeries.mapPolygons.template.states.create("active", {
                    fill: am5.color("#1B84FF")
                });

                // Add click event
                (this as any).polygonSeries.mapPolygons.template.on("click", (ev: any) => {
                    const dataItem = ev.target.dataItem;
                    const stateId = dataItem.get("id");
                    const stateName = dataItem.get("name");

                    this.onStateClick(stateId, stateName);
                });

                // Make the chart appear
                (this as any).chart.appear(1000, 100);

                // Update map data after everything is set up
                this.updateMapData();
            } catch (error) {
                console.error('Failed to initialize map:', error);
            }
        },

        updateMapData() {
            if (!(this as any).polygonSeries) return;

            try {
                // Process student data by state code
                const stateStudentCounts: Record<string, number> = {};

                this.students.forEach(student => {
                    if (student.location) {
                        // Map Australian state codes to amCharts geodata IDs
                        const stateMapping: Record<string, string> = {
                            'NSW': 'AU-NSW',
                            'VIC': 'AU-VIC',
                            'QLD': 'AU-QLD',
                            'SA': 'AU-SA',
                            'WA': 'AU-WA',
                            'TAS': 'AU-TAS',
                            'NT': 'AU-NT',
                            'ACT': 'AU-ACT'
                        };

                        const mappedStateId = stateMapping[student.location];
                        if (mappedStateId) {
                            stateStudentCounts[mappedStateId] = (stateStudentCounts[mappedStateId] || 0) + 1;
                        }
                    }
                });

                // Update polygon colors based on student counts
                (this as any).polygonSeries.mapPolygons.each((polygon: any) => {
                    const stateId = polygon.dataItem.get("id");
                    const studentCount = stateStudentCounts[stateId] || 0;

                    polygon.dataItem.set("studentCount", studentCount);

                    let color = "#74788d"; // Default gray - no students
                    if (studentCount > 0 && studentCount <= 5) {
                        color = "#E1F0FF"; // Light blue - 1-5 students
                    } else if (studentCount > 5 && studentCount <= 15) {
                        color = "#009EF7"; // Medium blue - 6-15 students
                    } else if (studentCount > 15) {
                        color = "#1B84FF"; // Dark blue - 16+ students
                    }

                    polygon.set("fill", am5.color(color));
                });
            } catch (error) {
                console.error('Failed to update map data:', error);
            }
        },

        async onStateClick(stateId: string, stateName: string) {
            this.selectedState = {
                id: stateId,
                name: stateName,
                code: stateId,
                studentCount: 0
            };

            // Show modal
            const modal = new (window as any).bootstrap.Modal(document.getElementById('stateStudentsModal'));
            modal.show();

            // Fetch students for this state
            await this.fetchStateStudents(stateId);
        },

        async fetchStateStudents(stateCode: string) {
            this.modalLoading = true;
            try {
                const response = await fetch(`/employer/get-students?state=${stateCode}`);
                const data = await response.json();
                this.stateStudents = data.result.data || [];
            } catch (error) {
                console.error('Error fetching state students:', error);
                this.stateStudents = [];
            } finally {
                this.modalLoading = false;
            }
        },

        async toggleFollow(studentId: number) {
            if (!studentId) return;

            // Set loading state
            this.followingStates[studentId] = { loading: true };

            try {
                const response = await fetch('/employer/toggle-follow-student', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('value') || '',
                    },
                    body: JSON.stringify({
                        student_id: studentId
                    })
                });

                if (!response.ok) {
                    throw new Error('Failed to toggle follow status');
                }

                const data = await response.json();

                // Update the student's follow status in the students array
                const student = this.stateStudents.find(s => s.id === studentId);
                if (student) {
                    student.is_following = data.is_following;
                }

            } catch (error) {
                console.error('Error toggling follow status:', error);
            } finally {
                // Remove loading state
                this.followingStates[studentId] = { loading: false };
            }
        }
    }
});
</script>

<style scoped>
.w-20px {
    width: 20px !important;
}

.h-20px {
    height: 20px !important;
}
</style>
