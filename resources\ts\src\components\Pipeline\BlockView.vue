<template>
    <div class="tab-pane fade show active" id="kt_project_users_card_pane">
        <div v-if="loading" class="d-flex justify-content-center py-10">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
        </div>

        <div v-else-if="students.length === 0" class="text-center py-10">
            <h4>No students found</h4>
            <p class="text-muted">Try adjusting your search criteria</p>
        </div>

        <div v-else class="row g-6 g-xl-9">
            <div class="col-md-6 col-xxl-4" v-for="student in students" :key="student.id">
                <div class="border p-7 rounded position-relative bg-white h-100">
                    <!-- Follow Button Positioned at Top Right -->
                    <div v-if="student.is_public" class="position-absolute top-0 end-0">
                        <button type="button" class="btn d-flex align-items-center gap-1 pt-8 border-0" style="background-color: transparent; color: rgb(171 183 197);" @click="toggleFollow(student.id!)" :disabled="followingStates[student.id!]?.loading">
                            <i :class="student.is_following ? 'bi bi-check pe-0 fs-2' : 'bi bi-plus pe-0 fs-2'"></i>
                            {{ student.is_following ? 'Following' : 'Follow' }}
                        </button>
                    </div>

                    <!-- Profile Section -->
                    <div class="ms-1 d-flex flex-row align-items-center gap-4 mt-5">
                        <div class="d-flex flex-column mt-10">
                            <div class="symbol symbol-125px symbol-circle mb-5">
                                <span  class="symbol-label fs-2x fw-semibold text-dark bg-secondary"><span v-if="student.is_public">{{ student.initials }}</span></span>
                            </div>
                            <p class="card-text mb-0">They are considering:</p>
                        </div>

                        <div class="d-flex flex-column mt-5 gap-2">
                            <h2>{{ student.name }}</h2>
                            <div class="date rounded">
                                <p class="badge badge-light-info">Year {{ student.year }}</p>
                            </div>
                        </div>
                    </div>

                    <!-- Centered Flex Items with Gap and Rounded Border -->
                    <div class="d-flex gap-14 mt-4">
                        <div class="p-2 rounded border border-2 border-dashed d-flex flex-column justify-content-center align-items-center" style="width:138px; height:80px;">
                            <h3 class="fs-6 fw-bold text-gray-700 mb-1">{{ student.industriesCount }}</h3>
                            <p class="fw-semibold text-gray-500 m-0">Industries</p>
                        </div>
                        <div class="p-2 rounded border border-2 border-dashed d-flex flex-column justify-content-center align-items-center" style="width:138px; height:80px;">
                            <h3 class="fs-6 fw-bold text-gray-700 mb-1">{{ student.jobsCount }}</h3>
                            <p class="fw-semibold text-gray-500 m-0">Jobs</p>
                        </div>
                        <div class="p-2 rounded border border-2 border-dashed d-flex flex-column justify-content-center align-items-center" style="width:138px; height:80px;">
                            <h3 class="fs-6 fw-bold text-gray-700 mb-1">{{ student.companiesCount }}</h3>
                            <p class="fw-semibold text-gray-500 m-0">Companies</p>
                        </div>
                    </div>

                    <div class="d-flex align-items-center justify-content-end mt-4">
                        <!-- View Profile Button -->
                        <a v-if="student.is_public" :href="`/profiles/edit/${student.id}`" class="btn btn-light d-flex align-items-center text-black gap-2">
                            View Profile
                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M17.25 8.25 21 12m0 0-3.75 3.75M21 12H3" />
                            </svg>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
    import { defineComponent } from 'vue';

    interface Student {
        id?: number;
        name?: string;
        email?: string;
        year?: string | null;
        initials?: string | null;
        industriesCount?: number;
        jobsCount?: number;
        companiesCount?: number;
        is_following?: boolean;
        is_public?: boolean;
    }

    export default defineComponent({
        name: 'PipelineBlockView',
        props: {
            students: {
                type: Array as () => Student[],
                required: true,
                default: () => []
            },
            loading: {
                type: Boolean,
                required: true,
                default: false
            }
        },
        data() {
            return {
                followingStates: {} as Record<number, { loading: boolean }>
            }
        },
        methods: {
            async toggleFollow(studentId: number) {
                if (!studentId) return;

                // Set loading state
                this.followingStates[studentId] = { loading: true };

                try {
                    const response = await fetch('/employer/toggle-follow-student', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('value') || '',
                        },
                        body: JSON.stringify({
                            student_id: studentId
                        })
                    });

                    if (!response.ok) {
                        throw new Error('Failed to toggle follow status');
                    }

                    const data = await response.json();

                    // Update the student's follow status in the students array
                    const student = this.students.find(s => s.id === studentId);
                    if (student) {
                        student.is_following = data.is_following;
                    }

                } catch (error) {
                    console.error('Error toggling follow status:', error);
                    // You might want to show a toast notification here
                } finally {
                    // Remove loading state
                    this.followingStates[studentId] = { loading: false };
                }
            }
        }
    })
</script>