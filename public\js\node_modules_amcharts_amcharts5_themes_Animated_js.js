"use strict";
(self["webpackChunk"] = self["webpackChunk"] || []).push([["node_modules_amcharts_amcharts5_themes_Animated_js"],{

/***/ "./node_modules/@amcharts/amcharts5/.internal/core/Theme.js":
/*!******************************************************************!*\
  !*** ./node_modules/@amcharts/amcharts5/.internal/core/Theme.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "Theme": () => (/* binding */ Theme)
/* harmony export */ });
/* harmony import */ var _util_Template__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./util/Template */ "./node_modules/@amcharts/amcharts5/.internal/core/util/Template.js");
/* harmony import */ var _util_Order__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./util/Order */ "./node_modules/@amcharts/amcharts5/.internal/core/util/Order.js");
/* harmony import */ var _util_Array__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./util/Array */ "./node_modules/@amcharts/amcharts5/.internal/core/util/Array.js");



/**
 * A base class for an amCharts theme.
 *
 * @see {@link https://www.amcharts.com/docs/v5/concepts/themes/} for more info
 * @important
 */
class Theme {
    constructor(root, isReal) {
        Object.defineProperty(this, "_root", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "_rules", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: {}
        });
        this._root = root;
        if (!isReal) {
            throw new Error("You cannot use `new Class()`, instead use `Class.new()`");
        }
    }
    /**
     * Use this method to create an instance of this class.
     *
     * @see {@link https://www.amcharts.com/docs/v5/getting-started/#New_element_syntax} for more info
     * @param   root      Root element
     * @param   settings  Settings
     * @param   template  Template
     * @return            Instantiated object
     */
    static new(root) {
        const x = (new this(root, true));
        x.setupDefaultRules();
        return x;
    }
    setupDefaultRules() { }
    /**
     * Looks up the rules for a specific theme class.
     *
     * @see {@link https://www.amcharts.com/docs/v5/themes/} for more info
     * @param   themeClass Theme class
     * @return             Array<IRule<A>>
     */
    _lookupRules(themeClass) {
        return this._rules[themeClass];
    }
    /**
     * Creates a [[Template]] for specific theme class and tags.
     *
     * NOTE: the difference from `rule()` is that `ruleRaw()` does not do any
     * type checks.
     *
     * @see {@link https://www.amcharts.com/docs/v5/themes/} for more info
     * @param   themeClass Theme class
     * @param   themeTags  Theme tags
     * @return             Template
     */
    ruleRaw(themeClass, themeTags = []) {
        let rules = this._rules[themeClass];
        if (!rules) {
            rules = this._rules[themeClass] = [];
        }
        themeTags.sort(_util_Order__WEBPACK_IMPORTED_MODULE_0__.compare);
        const { index, found } = _util_Array__WEBPACK_IMPORTED_MODULE_1__.getSortedIndex(rules, (x) => {
            const order = _util_Order__WEBPACK_IMPORTED_MODULE_0__.compare(x.tags.length, themeTags.length);
            if (order === 0) {
                return _util_Order__WEBPACK_IMPORTED_MODULE_0__.compareArray(x.tags, themeTags, _util_Order__WEBPACK_IMPORTED_MODULE_0__.compare);
            }
            else {
                return order;
            }
        });
        if (found) {
            return rules[index].template;
        }
        else {
            const template = _util_Template__WEBPACK_IMPORTED_MODULE_2__.Template["new"]({});
            rules.splice(index, 0, {
                tags: themeTags,
                template,
            });
            return template;
        }
    }
    /**
     * Creates a [[Template]] for specific theme class and tags.
     *
     * @see {@link https://www.amcharts.com/docs/v5/themes/} for more info
     * @param   themeClass Theme class
     * @param   themeTags  Theme tags
     * @return             Template
     */
    rule(themeClass, themeTags = []) {
        return this.ruleRaw(themeClass, themeTags);
    }
}
//# sourceMappingURL=Theme.js.map

/***/ }),

/***/ "./node_modules/@amcharts/amcharts5/.internal/core/util/Array.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@amcharts/amcharts5/.internal/core/util/Array.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "add": () => (/* binding */ add),
/* harmony export */   "any": () => (/* binding */ any),
/* harmony export */   "copy": () => (/* binding */ copy),
/* harmony export */   "each": () => (/* binding */ each),
/* harmony export */   "eachContinue": () => (/* binding */ eachContinue),
/* harmony export */   "eachReverse": () => (/* binding */ eachReverse),
/* harmony export */   "find": () => (/* binding */ find),
/* harmony export */   "findIndex": () => (/* binding */ findIndex),
/* harmony export */   "findIndexReverse": () => (/* binding */ findIndexReverse),
/* harmony export */   "findMap": () => (/* binding */ findMap),
/* harmony export */   "findReverse": () => (/* binding */ findReverse),
/* harmony export */   "first": () => (/* binding */ first),
/* harmony export */   "getFirstSortedIndex": () => (/* binding */ getFirstSortedIndex),
/* harmony export */   "getSortedIndex": () => (/* binding */ getSortedIndex),
/* harmony export */   "has": () => (/* binding */ has),
/* harmony export */   "indexOf": () => (/* binding */ indexOf),
/* harmony export */   "insert": () => (/* binding */ insert),
/* harmony export */   "insertIndex": () => (/* binding */ insertIndex),
/* harmony export */   "keepIf": () => (/* binding */ keepIf),
/* harmony export */   "last": () => (/* binding */ last),
/* harmony export */   "map": () => (/* binding */ map),
/* harmony export */   "move": () => (/* binding */ move),
/* harmony export */   "pushAll": () => (/* binding */ pushAll),
/* harmony export */   "pushOne": () => (/* binding */ pushOne),
/* harmony export */   "remove": () => (/* binding */ remove),
/* harmony export */   "removeFirst": () => (/* binding */ removeFirst),
/* harmony export */   "removeIndex": () => (/* binding */ removeIndex),
/* harmony export */   "replace": () => (/* binding */ replace),
/* harmony export */   "setIndex": () => (/* binding */ setIndex),
/* harmony export */   "shiftLeft": () => (/* binding */ shiftLeft),
/* harmony export */   "shuffle": () => (/* binding */ shuffle),
/* harmony export */   "slice": () => (/* binding */ slice),
/* harmony export */   "toArray": () => (/* binding */ toArray)
/* harmony export */ });
/* harmony import */ var _Type__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Type */ "./node_modules/@amcharts/amcharts5/.internal/core/util/Type.js");

/**
 * ============================================================================
 * UTILITY FUNCTIONS
 * ============================================================================
 * @hidden
 */
/**
 * Searches `array` for `value`.
 *
 * Returns -1 if not found.
 *
 * @param array  Source array
 * @param value  Value to search
 * @returns Index
 */
function indexOf(array, value) {
    const length = array.length;
    for (let i = 0; i < length; ++i) {
        // TODO handle NaN
        if (array[i] === value) {
            return i;
        }
    }
    return -1;
}
/**
 * Calls `test` for each element in `array`.
 *
 * If `test` returns `true` then it immediately returns `true`.
 *
 * If `test` returns `false` for all of the elements in `array` then it returns `false`.
 *
 * @param array  Source array
 * @param test   Function which is called on each element
 * @returns Whether `test` returned true or not
 */
function any(array, test) {
    const length = array.length;
    for (let i = 0; i < length; ++i) {
        if (test(array[i])) {
            return true;
        }
    }
    return false;
}
/**
 * Calls `fn` function for every member of array and returns a new array out
 * of all outputs.
 *
 * @param array  Source array
 * @param fn     Callback function
 * @returns New array
 */
function map(array, fn) {
    const length = array.length;
    const output = new Array(length);
    for (let i = 0; i < length; ++i) {
        output[i] = fn(array[i], i);
    }
    return output;
}
/**
 * Iterates through all items in array and calls `fn` function for each of
 * them.
 *
 * @param array  Source array
 * @param fn     Callback function
 */
function each(array, fn) {
    const length = array.length;
    for (let i = 0; i < length; ++i) {
        fn(array[i], i);
    }
}
/**
 * Iterates through all items in array in reverse order and calls `fn` function for each of
 * them.
 *
 * @param array  Source array
 * @param fn     Callback function
 */
function eachReverse(array, fn) {
    let i = array.length;
    while (i > 0) {
        --i;
        fn(array[i], i);
    }
}
/**
 * Iterates through all items in array and calls `fn` function for each of
 * them.
 *
 * If `fn` call evaluates to `false`, further iteration is cancelled.
 *
 * @param array  Source array
 * @param fn     Callback function
 */
function eachContinue(array, fn) {
    const length = array.length;
    for (let i = 0; i < length; ++i) {
        if (!fn(array[i], i)) {
            break;
        }
    }
}
/**
 * Shifts an item at `index` towards beginning of the array.
 *
 * @param array  Source array
 * @param index  Target element index
 */
function shiftLeft(array, index) {
    const length = array.length;
    for (let i = index; i < length; ++i) {
        array[i - index] = array[i];
    }
    array.length = length - index;
}
/**
 * Returns the last item of the array.
 *
 * @param array  Source array
 * @returns Last item
 */
function last(array) {
    const length = array.length;
    return length ? array[length - 1] : undefined;
}
/**
 * Returns the first item of the array.
 *
 * @param array  Source array
 * @returns Last item
 */
function first(array) {
    return array[0];
}
/**
 * Inserts `element` into `array` at `index`.
 *
 * Caps `index` to be between `0` and `array.length`
 *
 * @param array    Source array
 * @param element  Item to insert
 * @param array    Index to insert item at
 */
function insert(array, element, index) {
    //if (array) {
    index = Math.max(0, Math.min(index, array.length));
    array.splice(index, 0, element);
    //}
}
/**
 * Removes all copies of `element` from `array` (if they exist) and then
 * inserts `element` at `index`.
 *
 * @param array    Source array
 * @param element  Item
 * @param array    Index to move item to
 */
function setIndex(array, element, index) {
    remove(array, element);
    insert(array, element, index);
}
/**
 * Pushes all of the elements from `input` into `array`.
 *
 * @param array  Output array
 * @param input  Input array
 */
function pushAll(array, input) {
    const length = input.length;
    for (let i = 0; i < length; ++i) {
        array.push(input[i]);
    }
}
/**
 * Removes `element` from `array`.
 *
 * If there are multiple copies of `element`, they are all removed.
 *
 * @param array    Source array
 * @param element  Item to remove
 */
function remove(array, element) {
    let found = false;
    let index = 0;
    for (;;) {
        index = array.indexOf(element, index);
        if (index === -1) {
            return found;
        }
        else {
            found = true;
            array.splice(index, 1);
        }
    }
}
function removeFirst(array, element) {
    let index = array.indexOf(element);
    if (index !== -1) {
        array.splice(index, 1);
        return true;
    }
    else {
        return false;
    }
}
/**
 * Adds an `element` to `array`.
 *
 * If array already contains and item like this, it is removed before adding
 * it again.
 *
 * Optionally `toIndex` can be specified to add element at specific index.
 *
 * @param array    Source array
 * @param element  Item to add
 * @param array    Index to move item to
 */
function move(array, element, toIndex) {
    // @todo this implementation must be the same as the List.moveValue method
    // @todo don't do anything if the desired index is the same as the current index
    let index = indexOf(array, element);
    // @todo remove all old values rather than only the first ?
    if (index !== -1) {
        removeIndex(array, index);
    }
    if (toIndex == null) {
        array.push(element);
    }
    else {
        insertIndex(array, toIndex, element);
    }
}
/**
 * Inserts `element` into `array` at `index`.
 *
 * If `index` is not provided, it will insert `element` at the end of `array`.
 *
 * @param array    Source array
 * @param element  Item to add
 * @param array    Index to add item at
 */
function add(array, element, index) {
    // Append to the end if index is not set
    if (!_Type__WEBPACK_IMPORTED_MODULE_0__.isNumber(index)) {
        array.push(element);
    }
    // Add to the beginning of array if index is 0
    else if (index === 0) {
        array.unshift(element);
    }
    // Add to indicated place if index is set
    else {
        array.splice(index, 0, element);
    }
}
/**
 * Pushes `element` into `array` if it doesn't already exist.
 *
 * @param array    Source array
 * @param element  Item to add
 */
function pushOne(array, element) {
    if (array.indexOf(element) === -1) {
        array.push(element);
    }
}
/**
 * Removes `element` from `array` (if it exists) and then inserts `element` at
 * `index`.
 *
 * If `index` is not provided, it will insert `element` at the end of `array`.
 *
 * @param array    Source array
 * @param element  Item to remove
 * @param array    Index to move item to
 */
function replace(array, element, index) {
    // check if exists
    let ind = array.indexOf(element);
    // remove if exists
    if (ind !== -1) {
        array.splice(ind, 1);
    }
    // add to end if index is not set
    if (!_Type__WEBPACK_IMPORTED_MODULE_0__.isNumber(index)) {
        array.push(element);
    }
    // add to indicated place if index is set
    else {
        array.splice(index, 0, element);
    }
}
/**
 * Wraps `input` in an array, if it isn't already an array.
 *
 * @param input  Source value
 * @return An array
 */
function toArray(input) {
    if (Array.isArray(input)) {
        return input;
    }
    else {
        return [input];
    }
}
/**
 * Returns `true` if `element` exists in `array`.
 *
 * @param array    Source array
 * @param element  Item to search for
 * @returns Item in array?
 */
function has(array, element) {
    return indexOf(array, element) !== -1;
}
/**
 * Returns a shallow copy of `array`.
 *
 * @param array  Source array
 * @returns Copy of the array
 */
function copy(array) {
    const length = array.length;
    // It's faster to create the array with a pre-defined length
    const output = new Array(length);
    for (let i = 0; i < length; ++i) {
        // Because the array has a pre-defined length, we have to assign rather than push
        // This is also faster than pushing
        output[i] = array[i];
    }
    return output;
}
/**
 * Returns a copy of `array` which contains all the elements between `start`
 * and `end`. (including `start` and excluding `end`)
 *
 * If `end` is not provided, it defaults to `array.length`.
 *
 * @param array  Source array
 * @param start  Start index
 * @param end    End index
 * @returns Part of the array
 */
function slice(array, start, end = array.length) {
    const output = new Array(end - start);
    for (let i = start; i < end; ++i) {
        output[i - start] = array[i];
    }
    return output;
}
/**
 * Inserts a value into array at specific index.
 *
 * @param array  Source array
 * @param index  Index
 * @param value  Value to insert
 */
function insertIndex(array, index, value) {
    array.splice(index, 0, value);
}
/**
 * Removes a value from array at specific index.
 *
 * @param array  Source array
 * @param index  Index
 */
function removeIndex(array, index) {
    array.splice(index, 1);
}
/**
 * Searches the array using custom function and returns index of the item if
 * found.
 *
 * Will call `matches` function on all items of the array. If return value
 * evaluates to `true`, index is returned.
 *
 * Otherwise returns -1.
 *
 * @param array    Source array
 * @param matches  Search function
 * @returns Index of the item if found
 */
function findIndex(array, matches) {
    const length = array.length;
    for (let i = 0; i < length; ++i) {
        if (matches(array[i], i)) {
            return i;
        }
    }
    return -1;
}
/**
 * This is the same as `findIndex` except it searches from right to left.
 *
 * @param array    Source array
 * @param matches  Search function
 * @returns Index of the item if found
 */
function findIndexReverse(array, matches) {
    let i = array.length;
    while (i > 0) {
        --i;
        if (matches(array[i], i)) {
            return i;
        }
    }
    return -1;
}
/**
 * Searches the array using custom function and returns item if found.
 *
 * Will call `matches` function on all items of the array. If return value
 * evaluates to `true`, index is returned.
 *
 * Otherwise returns `undefined`.
 *
 * @param array    Source array
 * @param matches  Search function
 * @returns Item if found
 */
function find(array, matches) {
    const index = findIndex(array, matches);
    if (index !== -1) {
        return array[index];
    }
}
/**
 * This is the same as `find` except it searches from right to left.
 *
 * @param array    Source array
 * @param matches  Search function
 * @returns Item if found
 */
function findReverse(array, matches) {
    const index = findIndexReverse(array, matches);
    if (index !== -1) {
        return array[index];
    }
}
/**
 * Searches the array using custom function and returns item if found.
 *
 * Will call `matches` function on all items of the array. If value
 * is not `undefined`, it returns it.
 *
 * Otherwise returns `undefined`.
 *
 * @param array    Source array
 * @param matches  Search function
 * @returns Item if found
 */
function findMap(array, matches) {
    const length = array.length;
    for (let i = 0; i < length; ++i) {
        const value = matches(array[i], i);
        if (value !== undefined) {
            return value;
        }
    }
}
/**
 * Iterates through all items in array and calls `fn` function for each of
 * them.
 *
 * @param array  Source array
 * @param fn     Callback function
 */
function shuffle(array) {
    // https://stackoverflow.com/a/2450976/449477
    let currentIndex = array.length, temporaryValue, randomIndex;
    // While there remain elements to shuffle...
    while (0 !== currentIndex) {
        // Pick a remaining element...
        randomIndex = Math.floor(Math.random() * currentIndex);
        currentIndex -= 1;
        // And swap it with the current element.
        temporaryValue = array[currentIndex];
        array[currentIndex] = array[randomIndex];
        array[randomIndex] = temporaryValue;
    }
}
/**
 * Orders an array using specific `ordering` function and returns right-most index of
 * the `value`.
 *
 * @ignore Exclude from docs
 * @param array     Source array
 * @param ordering  An ordering function
 * @returns Result of the search
 */
function getSortedIndex(array, ordering) {
    let start = 0;
    let end = array.length;
    let found = false;
    while (start < end) {
        // TODO is this faster/slower than using Math.floor ?
        const pivot = (start + end) >> 1;
        const order = ordering(array[pivot]);
        // less
        if (order < 0) {
            start = pivot + 1;
            // equal
        }
        else if (order === 0) {
            found = true;
            start = pivot + 1;
            // more
        }
        else {
            end = pivot;
        }
    }
    return {
        found: found,
        index: (found ? start - 1 : start)
    };
}
/**
 * Orders an array using specific `ordering` function and returns left-most index of
 * the `value`.
 *
 * @ignore Exclude from docs
 * @param array     Source array
 * @param ordering  An ordering function
 * @returns Result of the search
 */
function getFirstSortedIndex(array, ordering) {
    let start = 0;
    let end = array.length;
    let found = false;
    while (start < end) {
        // TODO is this faster/slower than using Math.floor ?
        const pivot = (start + end) >> 1;
        const order = ordering(array[pivot]);
        // less
        if (order < 0) {
            start = pivot + 1;
            // equal
        }
        else if (order === 0) {
            found = true;
            end = pivot;
            // more
        }
        else {
            end = pivot;
        }
    }
    return {
        found: found,
        index: start
    };
}
function keepIf(array, keep) {
    let i = array.length;
    while (i > 0) {
        --i;
        if (!keep(array[i])) {
            array.splice(i, 1);
        }
    }
}
//# sourceMappingURL=Array.js.map

/***/ }),

/***/ "./node_modules/@amcharts/amcharts5/.internal/core/util/Disposer.js":
/*!**************************************************************************!*\
  !*** ./node_modules/@amcharts/amcharts5/.internal/core/util/Disposer.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "ArrayDisposer": () => (/* binding */ ArrayDisposer),
/* harmony export */   "CounterDisposer": () => (/* binding */ CounterDisposer),
/* harmony export */   "Disposer": () => (/* binding */ Disposer),
/* harmony export */   "DisposerClass": () => (/* binding */ DisposerClass),
/* harmony export */   "MultiDisposer": () => (/* binding */ MultiDisposer),
/* harmony export */   "MutableValueDisposer": () => (/* binding */ MutableValueDisposer)
/* harmony export */ });
/* harmony import */ var _Array__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Array */ "./node_modules/@amcharts/amcharts5/.internal/core/util/Array.js");
/**
 * ============================================================================
 * IMPORTS
 * ============================================================================
 * @hidden
 */

/**
 * A base class for disposable objects.
 *
 * @ignore Exclude from docs
 */
class DisposerClass {
    /**
     * Constructor.
     */
    constructor() {
        /**
         * Is object disposed?
         */
        Object.defineProperty(this, "_disposed", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        this._disposed = false;
    }
    /**
     * Checks if object is disposed.
     *
     * @return Disposed?
     */
    isDisposed() {
        return this._disposed;
    }
    /**
     * Disposes the object.
     */
    dispose() {
        if (!this._disposed) {
            this._disposed = true;
            this._dispose();
        }
    }
}
/**
 * A class for creating an IDisposer.
 *
 * @ignore Exclude from docs
 */
class Disposer {
    /**
     * Constructor.
     *
     * @param dispose  Function that disposes object
     */
    constructor(dispose) {
        /**
         * Is object disposed?
         */
        Object.defineProperty(this, "_disposed", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        /**
         * Method that disposes the object.
         */
        Object.defineProperty(this, "_dispose", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        this._disposed = false;
        this._dispose = dispose;
    }
    /**
     * Checks if object is disposed.
     *
     * @return Disposed?
     */
    isDisposed() {
        return this._disposed;
    }
    /**
     * Disposes the object.
     */
    dispose() {
        if (!this._disposed) {
            this._disposed = true;
            this._dispose();
        }
    }
}
/**
 * This can be extended by other classes to add a `_disposers` property.
 *
 * @ignore Exclude from docs
 */
class ArrayDisposer extends DisposerClass {
    constructor() {
        super(...arguments);
        Object.defineProperty(this, "_disposers", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: []
        });
    }
    _dispose() {
        _Array__WEBPACK_IMPORTED_MODULE_0__.each(this._disposers, (x) => {
            x.dispose();
        });
    }
}
/**
 * A collection of related disposers that can be disposed in one go.
 *
 * @ignore Exclude from docs
 */
class MultiDisposer extends DisposerClass {
    constructor(disposers) {
        super();
        Object.defineProperty(this, "_disposers", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        this._disposers = disposers;
    }
    _dispose() {
        _Array__WEBPACK_IMPORTED_MODULE_0__.each(this._disposers, (x) => {
            x.dispose();
        });
    }
    get disposers() {
        return this._disposers;
    }
}
/**
 * A special kind of Disposer that has attached value set.
 *
 * If a new value is set using `set()` method, the old disposer value is
 * disposed.
 *
 * @ignore Exclude from docs
 * @todo Description
 */
class MutableValueDisposer extends DisposerClass {
    constructor() {
        super(...arguments);
        /**
         * Current disposer.
         */
        Object.defineProperty(this, "_disposer", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        /**
         * Current value.
         */
        Object.defineProperty(this, "_value", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
    }
    _dispose() {
        if (this._disposer != null) {
            this._disposer.dispose();
            this._disposer = undefined;
        }
    }
    /**
     * Returns current value.
     *
     * @return Value
     */
    get() {
        return this._value;
    }
    /**
     * Sets value and disposes previous disposer if it was set.
     *
     * @param value     New value
     * @param disposer  Disposer
     */
    set(value, disposer) {
        if (this._disposer != null) {
            this._disposer.dispose();
        }
        this._disposer = disposer;
        this._value = value;
    }
    /**
     * Resets the disposer value.
     */
    reset() {
        this.set(undefined, undefined);
    }
}
/**
 * @ignore Exclude from docs
 * @todo Description
 */
class CounterDisposer extends Disposer {
    constructor() {
        super(...arguments);
        /**
         * [_counter description]
         *
         * @todo Description
         */
        Object.defineProperty(this, "_counter", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: 0
        });
    }
    /**
     * [increment description]
     *
     * @todo Description
     */
    increment() {
        // TODO throw an error if it is disposed
        ++this._counter;
        // TODO make this more efficient
        return new Disposer(() => {
            --this._counter;
            if (this._counter === 0) {
                this.dispose();
            }
        });
    }
}
//# sourceMappingURL=Disposer.js.map

/***/ }),

/***/ "./node_modules/@amcharts/amcharts5/.internal/core/util/EventDispatcher.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/@amcharts/amcharts5/.internal/core/util/EventDispatcher.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "EventDispatcher": () => (/* binding */ EventDispatcher),
/* harmony export */   "TargetedEventDispatcher": () => (/* binding */ TargetedEventDispatcher)
/* harmony export */ });
/* harmony import */ var _Disposer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Disposer */ "./node_modules/@amcharts/amcharts5/.internal/core/util/Disposer.js");
/* harmony import */ var _Array__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Array */ "./node_modules/@amcharts/amcharts5/.internal/core/util/Array.js");
/* harmony import */ var _Type__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Type */ "./node_modules/@amcharts/amcharts5/.internal/core/util/Type.js");
/**
 * Event Dispatcher module is used for registering listeners and dispatching
 * events across amCharts system.
 */
/**
 * ============================================================================
 * IMPORTS
 * ============================================================================
 * @hidden
 */



/**
 * Universal Event Dispatcher.
 *
 * @see {@link https://www.amcharts.com/docs/v5/concepts/events/} for more info
 */
class EventDispatcher {
    /**
     * Constructor
     */
    constructor() {
        Object.defineProperty(this, "_listeners", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "_killed", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "_disabled", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "_iterating", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "_enabled", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "_disposed", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        this._listeners = [];
        this._killed = [];
        this._disabled = {};
        this._iterating = 0;
        this._enabled = true;
        this._disposed = false;
    }
    /**
     * Returns if this object has been already disposed.
     *
     * @return Disposed?
     */
    isDisposed() {
        return this._disposed;
    }
    /**
     * Dispose (destroy) this object.
     */
    dispose() {
        if (!this._disposed) {
            this._disposed = true;
            const a = this._listeners;
            this._iterating = 1;
            this._listeners = null;
            this._disabled = null;
            try {
                _Array__WEBPACK_IMPORTED_MODULE_0__.each(a, (x) => {
                    x.disposer.dispose();
                });
            }
            finally {
                this._killed = null;
                this._iterating = null;
            }
        }
    }
    /**
     * Checks if this particular event dispatcher has any listeners set.
     *
     * @return Has listeners?
     */
    hasListeners() {
        return this._listeners.length !== 0;
    }
    /**
     * Checks if this particular event dispatcher has any particular listeners set.
     *
     * @return Has particular event listeners?
     */
    hasListenersByType(type) {
        return _Array__WEBPACK_IMPORTED_MODULE_0__.any(this._listeners, (x) => (x.type === null || x.type === type) && !x.killed);
    }
    /**
     * Enable dispatching of events if they were previously disabled by
     * `disable()`.
     */
    enable() {
        this._enabled = true;
    }
    /**
     * Disable dispatching of events until re-enabled by `enable()`.
     */
    disable() {
        this._enabled = false;
    }
    /**
     * Enable dispatching particular event, if it was disabled before by
     * `disableType()`.
     *
     * @param type Event type
     */
    enableType(type) {
        delete this._disabled[type];
    }
    /**
     * Disable dispatching of events for a certain event type.
     *
     * Optionally, can set how many dispatches to skip before automatically
     * re-enabling the dispatching.
     *
     * @param type    Event type
     * @param amount  Number of event dispatches to skip
     */
    disableType(type, amount = Infinity) {
        this._disabled[type] = amount;
    }
    /**
     * Removes listener from dispatcher.
     *
     * Will throw an exception if such listener does not exists.
     *
     * @param listener Listener to remove
     */
    _removeListener(listener) {
        if (this._iterating === 0) {
            const index = this._listeners.indexOf(listener);
            if (index === -1) {
                throw new Error("Invalid state: could not remove listener");
            }
            this._listeners.splice(index, 1);
        }
        else {
            this._killed.push(listener);
        }
    }
    /**
     * Removes existing listener by certain parameters.
     *
     * @param once         Listener's once setting
     * @param type         Listener's type
     * @param callback     Callback function
     * @param context      Callback context
     */
    _removeExistingListener(once, type, callback, context) {
        if (this._disposed) {
            throw new Error("EventDispatcher is disposed");
        }
        this._eachListener((info) => {
            if (info.once === once && // TODO is this correct ?
                info.type === type &&
                (callback === undefined || info.callback === callback) &&
                info.context === context) {
                info.disposer.dispose();
            }
        });
    }
    /**
     * Checks if dispatching for particular event type is enabled.
     *
     * @param type  Event type
     * @return Enabled?
     */
    isEnabled(type) {
        if (this._disposed) {
            throw new Error("EventDispatcher is disposed");
        }
        // TODO is this check correct ?
        return this._enabled && this._listeners.length > 0 && this.hasListenersByType(type) && this._disabled[type] === undefined;
    }
    /**
     * Removes all listeners of a particular event type
     *
     * @param type  Listener's type
     */
    removeType(type) {
        if (this._disposed) {
            throw new Error("EventDispatcher is disposed");
        }
        this._eachListener((info) => {
            if (info.type === type) {
                info.disposer.dispose();
            }
        });
    }
    /**
     * Checks if there's already a listener with specific parameters.
     *
     * @param type      Listener's type
     * @param callback  Callback function
     * @param context   Callback context
     * @return Has listener?
     */
    has(type, callback, context) {
        const index = _Array__WEBPACK_IMPORTED_MODULE_0__.findIndex(this._listeners, (info) => {
            return info.once !== true && // Ignoring "once" listeners
                info.type === type &&
                (callback === undefined || info.callback === callback) &&
                info.context === context;
        });
        return index !== -1;
    }
    /**
     * Checks whether event of the particular type should be dispatched.
     *
     * @param type  Event type
     * @return Dispatch?
     */
    _shouldDispatch(type) {
        if (this._disposed) {
            throw new Error("EventDispatcher is disposed");
        }
        const count = this._disabled[type];
        if (!_Type__WEBPACK_IMPORTED_MODULE_1__.isNumber(count)) {
            return this._enabled;
        }
        else {
            if (count <= 1) {
                delete this._disabled[type];
            }
            else {
                --this._disabled[type];
            }
            return false;
        }
    }
    /**
     * [_eachListener description]
     *
     * All of this extra code is needed when a listener is removed while iterating
     *
     * @todo Description
     * @param fn [description]
     */
    _eachListener(fn) {
        ++this._iterating;
        try {
            _Array__WEBPACK_IMPORTED_MODULE_0__.each(this._listeners, fn);
        }
        finally {
            --this._iterating;
            // TODO should this be inside or outside the finally ?
            if (this._iterating === 0 && this._killed.length !== 0) {
                // Remove killed listeners
                _Array__WEBPACK_IMPORTED_MODULE_0__.each(this._killed, (killed) => {
                    this._removeListener(killed);
                });
                this._killed.length = 0;
            }
        }
    }
    /**
     * Dispatches an event immediately without waiting for next cycle.
     *
     * @param type   Event type
     * @param event  Event object
     * @todo automatically add in type and target properties if they are missing
     */
    dispatch(type, event) {
        if (this._shouldDispatch(type)) {
            // TODO check if it's faster to use an object of listeners rather than a single big array
            // TODO if the function throws, maybe it should keep going ?
            this._eachListener((listener) => {
                if (!listener.killed && (listener.type === null || listener.type === type)) {
                    listener.dispatch(type, event);
                }
            });
        }
    }
    /**
     * Shelves the event to be dispatched within next update cycle.
     *
     * @param type   Event type
     * @param event  Event object
     * @todo automatically add in type and target properties if they are missing
     */
    /*public dispatchLater<Key extends keyof T>(type: Key, event: T[Key]): void {
        if (this._shouldDispatch(type)) {
            this._eachListener((listener) => {
                // TODO check if it's faster to use an object of listeners rather than a single big array
                if (!listener.killed && (listener.type === null || listener.type === type)) {
                    // TODO if the function throws, maybe it should keep going ?
                    // TODO dispatch during the update cycle, rather than using whenIdle
                    $async.whenIdle(() => {
                        if (!listener.killed) {
                            listener.dispatch(type, event);
                        }
                    });
                }
            });
        }
    }*/
    /**
     * Creates, catalogs and returns an [[EventListener]].
     *
     * Event listener can be disposed.
     *
     * @param once         Listener's once setting
     * @param type         Listener's type
     * @param callback     Callback function
     * @param context      Callback context
     * @param shouldClone  Whether the listener should be copied when the EventDispatcher is copied
     * @param dispatch
     * @returns An event listener
     */
    _on(once, type, callback, context, shouldClone, dispatch) {
        if (this._disposed) {
            throw new Error("EventDispatcher is disposed");
        }
        this._removeExistingListener(once, type, callback, context);
        const info = {
            type: type,
            callback: callback,
            context: context,
            shouldClone: shouldClone,
            dispatch: dispatch,
            killed: false,
            once: once,
            disposer: new _Disposer__WEBPACK_IMPORTED_MODULE_2__.Disposer(() => {
                info.killed = true;
                this._removeListener(info);
            })
        };
        this._listeners.push(info);
        return info;
    }
    /**
     * Creates an event listener to be invoked on **any** event.
     *
     * @param callback     Callback function
     * @param context      Callback context
     * @param shouldClone  Whether the listener should be copied when the EventDispatcher is copied
     * @returns A disposable event listener
     */
    onAll(callback, context, shouldClone = true) {
        return this._on(false, null, callback, context, shouldClone, (_type, event) => callback.call(context, event)).disposer;
    }
    /**
     * Creates an event listener to be invoked on a specific event type.
     *
     * ```TypeScript
     * button.events.once("click", (ev) => {
     *   console.log("Button clicked");
     * }, this);
     * ```
     * ```JavaScript
     * button.events.once("click", (ev) => {
     *   console.log("Button clicked");
     * }, this);
     * ```
     *
     * The above will invoke our custom event handler whenever series we put
     * event on is hidden.
     *
     * @param type         Listener's type
     * @param callback     Callback function
     * @param context      Callback context
     * @param shouldClone  Whether the listener should be copied when the EventDispatcher is copied
     * @returns A disposable event listener
     */
    on(type, callback, context, shouldClone = true) {
        return this._on(false, type, callback, context, shouldClone, (_type, event) => callback.call(context, event)).disposer;
    }
    /**
     * Creates an event listener to be invoked on a specific event type once.
     *
     * Once the event listener is invoked, it is automatically disposed.
     *
     * ```TypeScript
     * button.events.once("click", (ev) => {
     *   console.log("Button clicked");
     * }, this);
     * ```
     * ```JavaScript
     * button.events.once("click", (ev) => {
     *   console.log("Button clicked");
     * }, this);
     * ```
     *
     * The above will invoke our custom event handler the first time series we
     * put event on is hidden.
     *
     * @param type         Listener's type
     * @param callback     Callback function
     * @param context      Callback context
     * @param shouldClone  Whether the listener should be copied when the EventDispatcher is copied
     * @returns A disposable event listener
     */
    once(type, callback, context, shouldClone = true) {
        const x = this._on(true, type, callback, context, shouldClone, (_type, event) => {
            x.disposer.dispose();
            callback.call(context, event);
        });
        // TODO maybe this should return a different Disposer ?
        return x.disposer;
    }
    /**
     * Removes the event listener with specific parameters.
     *
     * @param type         Listener's type
     * @param callback     Callback function
     * @param context      Callback context
     */
    off(type, callback, context) {
        this._removeExistingListener(false, type, callback, context);
    }
    /**
     * Copies all dispatcher parameters, including listeners, from another event
     * dispatcher.
     *
     * @param source Source event dispatcher
     * @ignore
     */
    copyFrom(source) {
        if (this._disposed) {
            throw new Error("EventDispatcher is disposed");
        }
        if (source === this) {
            throw new Error("Cannot copyFrom the same TargetedEventDispatcher");
        }
        const disposers = [];
        _Array__WEBPACK_IMPORTED_MODULE_0__.each(source._listeners, (x) => {
            // TODO is this correct ?
            if (!x.killed && x.shouldClone) {
                if (x.type === null) {
                    disposers.push(this.onAll(x.callback, x.context));
                }
                else if (x.once) {
                    disposers.push(this.once(x.type, x.callback, x.context));
                }
                else {
                    disposers.push(this.on(x.type, x.callback, x.context));
                }
            }
        });
        return new _Disposer__WEBPACK_IMPORTED_MODULE_2__.MultiDisposer(disposers);
    }
}
/**
 * A version of the [[EventDispatcher]] that dispatches events for a specific
 * target object.
 *
 * @ignore
 */
class TargetedEventDispatcher extends EventDispatcher {
    /**
     * Constructor
     *
     * @param target Event dispatcher target
     */
    constructor(target) {
        super();
        /**
         * A target object which is originating events using this dispatcher.
         */
        Object.defineProperty(this, "target", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        this.target = target;
    }
    /**
     * Copies all dispatcher parameters, including listeners, from another event
     * dispatcher.
     *
     * @param source Source event dispatcher
     * @ignore
     */
    copyFrom(source) {
        if (this._disposed) {
            throw new Error("EventDispatcher is disposed");
        }
        if (source === this) {
            throw new Error("Cannot copyFrom the same TargetedEventDispatcher");
        }
        const disposers = [];
        _Array__WEBPACK_IMPORTED_MODULE_0__.each(source._listeners, (x) => {
            // TODO very hacky
            if (x.context === source.target) {
                return;
            }
            // TODO is this correct ?
            if (!x.killed && x.shouldClone) {
                if (x.type === null) {
                    disposers.push(this.onAll(x.callback, x.context));
                }
                else if (x.once) {
                    disposers.push(this.once(x.type, x.callback, x.context));
                }
                else {
                    disposers.push(this.on(x.type, x.callback, x.context));
                }
            }
        });
        return new _Disposer__WEBPACK_IMPORTED_MODULE_2__.MultiDisposer(disposers);
    }
}
//# sourceMappingURL=EventDispatcher.js.map

/***/ }),

/***/ "./node_modules/@amcharts/amcharts5/.internal/core/util/Object.js":
/*!************************************************************************!*\
  !*** ./node_modules/@amcharts/amcharts5/.internal/core/util/Object.js ***!
  \************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "copy": () => (/* binding */ copy),
/* harmony export */   "each": () => (/* binding */ each),
/* harmony export */   "eachContinue": () => (/* binding */ eachContinue),
/* harmony export */   "eachOrdered": () => (/* binding */ eachOrdered),
/* harmony export */   "hasKey": () => (/* binding */ hasKey),
/* harmony export */   "keys": () => (/* binding */ keys),
/* harmony export */   "keysOrdered": () => (/* binding */ keysOrdered),
/* harmony export */   "softCopyProperties": () => (/* binding */ softCopyProperties)
/* harmony export */ });
/* harmony import */ var _Array__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Array */ "./node_modules/@amcharts/amcharts5/.internal/core/util/Array.js");

function keys(object) {
    return Object.keys(object);
}
/**
 * Returns an array of object's property names ordered using specific ordering
 * function.
 *
 * @param object  Source object
 * @param order   Ordering function
 * @returns Object property names
 */
function keysOrdered(object, order) {
    return keys(object).sort(order);
}
function copy(object) {
    return Object.assign({}, object);
}
function each(object, f) {
    keys(object).forEach((key) => {
        f(key, object[key]);
    });
}
/**
 * Iterates through all properties of the object calling `fn` for each of them.
 *
 * If return value of the function evaluates to `false` further iteration is
 * cancelled.
 *
 * @param object  Source object
 * @param fn      Callback function
 */
function eachContinue(object, fn) {
    for (let key in object) {
        if (hasKey(object, key)) {
            if (!fn(key, object[key])) {
                break;
            }
        }
    }
}
/**
 * Orders object properties using custom `ord` function and iterates through
 * them calling `fn` for each of them.
 *
 * @param object  Source object
 * @param fn      Callback function
 * @param order   Ordering function
 */
function eachOrdered(object, fn, ord) {
    _Array__WEBPACK_IMPORTED_MODULE_0__.each(keysOrdered(object, ord), (key) => {
        fn(key, object[key]);
    });
}
/**
 * Checks if `object` has a specific `key`.
 *
 * @param object  Source object
 * @param key     Property name
 * @returns Has key?
 */
function hasKey(object, key) {
    return {}.hasOwnProperty.call(object, key);
}
/**
 * Copies all properties of one object to the other, omitting undefined, but only if property in target object doesn't have a value set.
 *
 * @param fromObject  Source object
 * @param toObject    Target object
 * @return Updated target object
 * @todo Maybe consolidate with utils.copy?
 */
function softCopyProperties(source, target) {
    each(source, (key, value) => {
        // only if value is set
        //if ($type.hasValue(value) && !($type.hasValue((<any>target)[key]))) {
        if (value != null && target[key] == null) {
            target[key] = value;
        }
    });
    return target;
}
//# sourceMappingURL=Object.js.map

/***/ }),

/***/ "./node_modules/@amcharts/amcharts5/.internal/core/util/Order.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@amcharts/amcharts5/.internal/core/util/Order.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "compare": () => (/* binding */ compare),
/* harmony export */   "compareArray": () => (/* binding */ compareArray),
/* harmony export */   "compareNumber": () => (/* binding */ compareNumber),
/* harmony export */   "reverse": () => (/* binding */ reverse)
/* harmony export */ });
/**
 * @ignore
 */
function compare(left, right) {
    if (left === right) {
        return 0;
    }
    else if (left < right) {
        return -1;
    }
    else {
        return 1;
    }
}
/**
 * @ignore
 */
function compareArray(left, right, f) {
    const leftLength = left.length;
    const rightLength = right.length;
    const length = Math.min(leftLength, rightLength);
    for (let i = 0; i < length; ++i) {
        const order = f(left[i], right[i]);
        if (order !== 0) {
            return order;
        }
    }
    return compare(leftLength, rightLength);
}
/**
 * @ignore
 */
function reverse(order) {
    if (order < 0) {
        return 1;
    }
    else if (order > 0) {
        return -1;
    }
    else {
        return 0;
    }
}
/**
 * @ignore
 */
function compareNumber(a, b) {
    if (a === b) {
        return 0;
    }
    else if (a < b) {
        return -1;
    }
    else {
        return 1;
    }
}
//# sourceMappingURL=Order.js.map

/***/ }),

/***/ "./node_modules/@amcharts/amcharts5/.internal/core/util/Template.js":
/*!**************************************************************************!*\
  !*** ./node_modules/@amcharts/amcharts5/.internal/core/util/Template.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "Template": () => (/* binding */ Template),
/* harmony export */   "TemplateAdapters": () => (/* binding */ TemplateAdapters),
/* harmony export */   "TemplateState": () => (/* binding */ TemplateState),
/* harmony export */   "TemplateStates": () => (/* binding */ TemplateStates)
/* harmony export */ });
/* harmony import */ var _EventDispatcher__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./EventDispatcher */ "./node_modules/@amcharts/amcharts5/.internal/core/util/EventDispatcher.js");
/* harmony import */ var _Disposer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Disposer */ "./node_modules/@amcharts/amcharts5/.internal/core/util/Disposer.js");
/* harmony import */ var _Array__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Array */ "./node_modules/@amcharts/amcharts5/.internal/core/util/Array.js");
/* harmony import */ var _Object__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Object */ "./node_modules/@amcharts/amcharts5/.internal/core/util/Object.js");
/* harmony import */ var _Type__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Type */ "./node_modules/@amcharts/amcharts5/.internal/core/util/Type.js");





function disposeSettings(settings) {
    _Object__WEBPACK_IMPORTED_MODULE_0__.each(settings, (_key, value) => {
        if (_Type__WEBPACK_IMPORTED_MODULE_1__.isObject(value) && typeof value.dispose === "function") {
            value.enableDispose = true;
            value.dispose();
        }
    });
}
class TemplateState {
    constructor(name, template, settings) {
        Object.defineProperty(this, "_settings", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "_name", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "_template", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        this._name = name;
        this._template = template;
        this._settings = settings;
    }
    _dispose() {
        disposeSettings(this._settings);
    }
    get(key, fallback) {
        const value = this._settings[key];
        if (value !== undefined) {
            return value;
        }
        else {
            return fallback;
        }
    }
    set(key, value) {
        this._settings[key] = value;
        // TODO maybe only do this if the value changed ?
        this._template._stateChanged(this._name);
    }
    remove(key) {
        delete this._settings[key];
        // TODO maybe only do this if the value changed ?
        this._template._stateChanged(this._name);
    }
    setAll(settings) {
        _Object__WEBPACK_IMPORTED_MODULE_0__.keys(settings).forEach((key) => {
            this._settings[key] = settings[key];
        });
        this._template._stateChanged(this._name);
    }
    _apply(other, seen) {
        _Object__WEBPACK_IMPORTED_MODULE_0__.each(this._settings, (key, value) => {
            if (!seen[key] && !other._userSettings[key]) {
                seen[key] = true;
                other.setRaw(key, value);
            }
        });
    }
}
class TemplateStates {
    constructor(template) {
        Object.defineProperty(this, "_template", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "_states", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: {}
        });
        this._template = template;
    }
    _dispose() {
        _Object__WEBPACK_IMPORTED_MODULE_0__.each(this._states, (_key, state) => {
            state._dispose();
        });
    }
    lookup(name) {
        return this._states[name];
    }
    create(name, settings) {
        const state = this._states[name];
        if (state) {
            state.setAll(settings);
            return state;
        }
        else {
            const state = new TemplateState(name, this._template, settings);
            this._states[name] = state;
            this._template._stateChanged(name);
            return state;
        }
    }
    remove(name) {
        delete this._states[name];
        this._template._stateChanged(name);
    }
    _apply(entity, state) {
        _Object__WEBPACK_IMPORTED_MODULE_0__.each(this._states, (key, value) => {
            let seen = state.states[key];
            if (seen == null) {
                seen = state.states[key] = {};
            }
            const other = entity.states.create(key, {});
            value._apply(other, seen);
        });
    }
}
class TemplateAdapters {
    constructor() {
        Object.defineProperty(this, "_callbacks", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: {}
        });
    }
    add(key, callback) {
        let callbacks = this._callbacks[key];
        if (callbacks === undefined) {
            callbacks = this._callbacks[key] = [];
        }
        callbacks.push(callback);
        return new _Disposer__WEBPACK_IMPORTED_MODULE_2__.Disposer(() => {
            _Array__WEBPACK_IMPORTED_MODULE_3__.removeFirst(callbacks, callback);
            if (callbacks.length === 0) {
                delete this._callbacks[key];
            }
        });
    }
    remove(key) {
        const callbacks = this._callbacks[key];
        if (callbacks !== undefined) {
            delete this._callbacks[key];
        }
    }
    _apply(entity) {
        const disposers = [];
        _Object__WEBPACK_IMPORTED_MODULE_0__.each(this._callbacks, (key, callbacks) => {
            _Array__WEBPACK_IMPORTED_MODULE_3__.each(callbacks, (callback) => {
                disposers.push(entity.adapters.add(key, callback));
            });
        });
        return new _Disposer__WEBPACK_IMPORTED_MODULE_2__.MultiDisposer(disposers);
    }
}
// TODO maybe extend from Properties ?
class Template {
    constructor(settings, isReal) {
        Object.defineProperty(this, "_disposed", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: false
        });
        Object.defineProperty(this, "_settings", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "_privateSettings", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: {}
        });
        // TODO code duplication with Properties
        Object.defineProperty(this, "_settingEvents", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: {}
        });
        Object.defineProperty(this, "_privateSettingEvents", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: {}
        });
        Object.defineProperty(this, "_entities", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: []
        });
        Object.defineProperty(this, "states", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: new TemplateStates(this)
        });
        Object.defineProperty(this, "adapters", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: new TemplateAdapters()
        });
        Object.defineProperty(this, "events", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: new _EventDispatcher__WEBPACK_IMPORTED_MODULE_4__.EventDispatcher()
        });
        Object.defineProperty(this, "setup", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        if (!isReal) {
            throw new Error("You cannot use `new Class()`, instead use `Class.new()`");
        }
        this._settings = settings;
    }
    /**
     * Use this method to create an instance of this class.
     *
     * @see {@link https://www.amcharts.com/docs/v5/getting-started/#New_element_syntax} for more info
     * @param   root      Root element
     * @param   settings  Settings
     * @param   template  Template
     * @return            Instantiated object
     */
    static new(settings) {
        return new Template(settings, true);
    }
    _dispose() {
        disposeSettings(this._settings);
        disposeSettings(this._privateSettings);
    }
    /**
     * Returns `true` if this element is disposed.
     *
     * @return Disposed
     */
    isDisposed() {
        return this._disposed;
    }
    /**
     * Disposes this object.
     */
    dispose() {
        if (!this._disposed) {
            this._disposed = true;
            this._dispose();
        }
    }
    _checkDisposed() {
        if (this._disposed) {
            throw new Error("Template is disposed");
        }
    }
    /**
     * Array of all entities using this template.
     */
    get entities() {
        return this._entities;
    }
    get(key, fallback) {
        this._checkDisposed();
        const value = this._settings[key];
        if (value !== undefined) {
            return value;
        }
        else {
            return fallback;
        }
    }
    setRaw(key, value) {
        this._checkDisposed();
        this._settings[key] = value;
    }
    set(key, value) {
        this._checkDisposed();
        if (this._settings[key] !== value) {
            this.setRaw(key, value);
            this._entities.forEach((entity) => {
                entity._setTemplateProperty(this, key, value);
            });
        }
    }
    remove(key) {
        this._checkDisposed();
        if (key in this._settings) {
            delete this._settings[key];
            this._entities.forEach((entity) => {
                entity._removeTemplateProperty(key);
            });
        }
    }
    removeAll() {
        this._checkDisposed();
        _Object__WEBPACK_IMPORTED_MODULE_0__.each(this._settings, (key, _value) => {
            this.remove(key);
        });
    }
    getPrivate(key, fallback) {
        this._checkDisposed();
        const value = this._privateSettings[key];
        if (value !== undefined) {
            return value;
        }
        else {
            return fallback;
        }
    }
    setPrivateRaw(key, value) {
        this._checkDisposed();
        this._privateSettings[key] = value;
        return value;
    }
    setPrivate(key, value) {
        this._checkDisposed();
        if (this._privateSettings[key] !== value) {
            this.setPrivateRaw(key, value);
            this._entities.forEach((entity) => {
                entity._setTemplatePrivateProperty(this, key, value);
            });
        }
        return value;
    }
    removePrivate(key) {
        this._checkDisposed();
        if (key in this._privateSettings) {
            delete this._privateSettings[key];
            this._entities.forEach((entity) => {
                entity._removeTemplatePrivateProperty(key);
            });
        }
    }
    setAll(value) {
        this._checkDisposed();
        _Object__WEBPACK_IMPORTED_MODULE_0__.each(value, (key, value) => {
            this.set(key, value);
        });
    }
    // TODO code duplication with Properties
    on(key, callback) {
        this._checkDisposed();
        let events = this._settingEvents[key];
        if (events === undefined) {
            events = this._settingEvents[key] = [];
        }
        events.push(callback);
        return new _Disposer__WEBPACK_IMPORTED_MODULE_2__.Disposer(() => {
            _Array__WEBPACK_IMPORTED_MODULE_3__.removeFirst(events, callback);
            if (events.length === 0) {
                delete this._settingEvents[key];
            }
        });
    }
    // TODO code duplication with Properties
    onPrivate(key, callback) {
        this._checkDisposed();
        let events = this._privateSettingEvents[key];
        if (events === undefined) {
            events = this._privateSettingEvents[key] = [];
        }
        events.push(callback);
        return new _Disposer__WEBPACK_IMPORTED_MODULE_2__.Disposer(() => {
            _Array__WEBPACK_IMPORTED_MODULE_3__.removeFirst(events, callback);
            if (events.length === 0) {
                delete this._privateSettingEvents[key];
            }
        });
    }
    _apply(entity, state) {
        this._checkDisposed();
        const disposers = [];
        _Object__WEBPACK_IMPORTED_MODULE_0__.each(this._settingEvents, (key, events) => {
            _Array__WEBPACK_IMPORTED_MODULE_3__.each(events, (event) => {
                disposers.push(entity.on(key, event));
            });
        });
        _Object__WEBPACK_IMPORTED_MODULE_0__.each(this._privateSettingEvents, (key, events) => {
            _Array__WEBPACK_IMPORTED_MODULE_3__.each(events, (event) => {
                disposers.push(entity.onPrivate(key, event));
            });
        });
        this.states._apply(entity, state);
        disposers.push(this.adapters._apply(entity));
        disposers.push(entity.events.copyFrom(this.events));
        return new _Disposer__WEBPACK_IMPORTED_MODULE_2__.MultiDisposer(disposers);
    }
    _setObjectTemplate(entity) {
        this._checkDisposed();
        this._entities.push(entity);
    }
    _removeObjectTemplate(entity) {
        //this._checkDisposed();
        _Array__WEBPACK_IMPORTED_MODULE_3__.remove(this._entities, entity);
    }
    _stateChanged(name) {
        this._checkDisposed();
        this._entities.forEach((entity) => {
            entity._applyStateByKey(name);
        });
    }
}
//# sourceMappingURL=Template.js.map

/***/ }),

/***/ "./node_modules/@amcharts/amcharts5/.internal/core/util/Type.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@amcharts/amcharts5/.internal/core/util/Type.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "PLACEHOLDER": () => (/* binding */ PLACEHOLDER),
/* harmony export */   "PLACEHOLDER2": () => (/* binding */ PLACEHOLDER2),
/* harmony export */   "assert": () => (/* binding */ assert),
/* harmony export */   "getType": () => (/* binding */ getType),
/* harmony export */   "isArray": () => (/* binding */ isArray),
/* harmony export */   "isDate": () => (/* binding */ isDate),
/* harmony export */   "isNaN": () => (/* binding */ isNaN),
/* harmony export */   "isNumber": () => (/* binding */ isNumber),
/* harmony export */   "isObject": () => (/* binding */ isObject),
/* harmony export */   "isString": () => (/* binding */ isString),
/* harmony export */   "numberToString": () => (/* binding */ numberToString),
/* harmony export */   "repeat": () => (/* binding */ repeat),
/* harmony export */   "toDate": () => (/* binding */ toDate),
/* harmony export */   "toNumber": () => (/* binding */ toNumber)
/* harmony export */ });
/**
 * A collection of utility functions for various type checks and conversion
 * @hidden
 */
/**
 * ============================================================================
 * TYPE CHECK
 * ============================================================================
 * @hidden
 */
/**
 * Returns `true` if value is not a number (NaN).
 *
 * @param value Input value
 * @return Is NaN?
 */
function isNaN(value) {
    return Number(value) !== value;
}
/**
 * Returns a type of the value.
 *
 * @param value  Input value
 * @return Type of the value
 * @ignore
 */
function getType(value) {
    return ({}).toString.call(value);
}
/**
 * Asserts that the condition is true.
 *
 * @param condition  Condition to check
 * @param message    Message to display in the error
 * @ignore
 */
function assert(condition, message = "Assertion failed") {
    if (!condition) {
        throw new Error(message);
    }
}
/**
 * ============================================================================
 * QUICK CONVERSION
 * ============================================================================
 * @hidden
 */
/**
 * Converts any value into a `number`.
 *
 * @param value  Source value
 * @return Number representation of value
 */
function toNumber(value) {
    if (value != null && !isNumber(value)) {
        let converted = Number(value);
        if (isNaN(converted) && isString(value) && value != "" && value.match(/[0-9]+/)) {
            return toNumber(value.replace(/[^0-9.\-]+/g, ''));
        }
        return converted;
    }
    return value;
}
/**
 * Converts anything to Date object.
 *
 * @param value  A value of any type
 * @return Date object representing a value
 */
function toDate(value) {
    if (isDate(value)) {
        // TODO maybe don't create a new Date ?
        return new Date(value);
    }
    else if (isNumber(value)) {
        return new Date(value);
    }
    else {
        // Try converting to number (assuming timestamp)
        let num = Number(value);
        if (!isNumber(num)) {
            return new Date(value);
        }
        else {
            return new Date(num);
        }
    }
}
/**
 * Converts numeric value into string. Deals with large or small numbers that
 * would otherwise use exponents.
 *
 * @param value  Numeric value
 * @return Numeric value as string
 */
function numberToString(value) {
    // TODO handle Infinity and -Infinity
    if (isNaN(value)) {
        return "NaN";
    }
    if (value === Infinity) {
        return "Infinity";
    }
    if (value === -Infinity) {
        return "-Infinity";
    }
    // Negative 0
    if ((value === 0) && (1 / value === -Infinity)) {
        return "-0";
    }
    // Preserve negative and deal with absoute values
    let negative = value < 0;
    value = Math.abs(value);
    // TODO test this
    let parsed = /^([0-9]+)(?:\.([0-9]+))?(?:e[\+\-]([0-9]+))?$/.exec("" + value);
    let digits = parsed[1];
    let decimals = parsed[2] || "";
    let res;
    // Leave the nummber as it is if it does not use exponents
    if (parsed[3] === undefined) {
        res = (decimals === "" ? digits : digits + "." + decimals);
    }
    else {
        let exponent = +parsed[3];
        // Deal with decimals
        if (value < 1) {
            let zeros = exponent - 1;
            res = "0." + repeat("0", zeros) + digits + decimals;
            // Deal with integers
        }
        else {
            let zeros = exponent - decimals.length;
            if (zeros === 0) {
                res = digits + decimals;
            }
            else if (zeros < 0) {
                res = digits + decimals.slice(0, zeros) + "." + decimals.slice(zeros);
            }
            else {
                res = digits + decimals + repeat("0", zeros);
            }
        }
    }
    return negative ? "-" + res : res;
}
/**
 * Repeats a `string` number of times as set in `amount`.
 *
 * @ignore Exclude from docs
 * @todo Make this faster
 * @param string  Source string
 * @param amount  Number of times to repeat string
 * @return New string
 */
function repeat(string, amount) {
    return new Array(amount + 1).join(string);
}
/**
 * ============================================================================
 * TYPE CHECK
 * ============================================================================
 * @hidden
 */
/**
 * Checks if parameter is `Date`.
 *
 * @param value  Input value
 * @return Is Date?
 */
function isDate(value) {
    return getType(value) === "[object Date]";
}
/**
 * Checks if parameter is `string`.
 *
 * @param value  Input value
 * @return Is string?
 */
function isString(value) {
    return typeof value === "string";
}
/**
 * Checks if parameter is `number`.
 *
 * @param value  Input value
 * @return Is number?
 */
function isNumber(value) {
    return typeof value === "number" && Number(value) == value;
}
/**
 * Checks if parameter is `object`.
 *
 * @param value  Input value
 * @return Is object?
 */
function isObject(value) {
    return typeof value === "object" && value !== null;
}
/**
 * Checks if parameter is `Array`.
 *
 * @param value  Input value
 * @return Is Array?
 */
function isArray(value) {
    return Array.isArray(value);
}
/**
 * ============================================================================
 * STATIC CONSTANTS
 * ============================================================================
 * @hidden
 */
/**
 * @ignore Exclude from docs
 */
const PLACEHOLDER = "__§§§__";
/**
 * @ignore Exclude from docs
 */
const PLACEHOLDER2 = "__§§§§__";
//# sourceMappingURL=Type.js.map

/***/ }),

/***/ "./node_modules/@amcharts/amcharts5/.internal/themes/AnimatedTheme.js":
/*!****************************************************************************!*\
  !*** ./node_modules/@amcharts/amcharts5/.internal/themes/AnimatedTheme.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "AnimatedTheme": () => (/* binding */ AnimatedTheme)
/* harmony export */ });
/* harmony import */ var _core_Theme__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../core/Theme */ "./node_modules/@amcharts/amcharts5/.internal/core/Theme.js");

/**
 * @ignore
 */
class AnimatedTheme extends _core_Theme__WEBPACK_IMPORTED_MODULE_0__.Theme {
    setupDefaultRules() {
        super.setupDefaultRules();
        this.rule("Component").setAll({
            interpolationDuration: 600
        });
        this.rule("Hierarchy").set("animationDuration", 600);
        this.rule("Scrollbar").set("animationDuration", 600);
        this.rule("Tooltip").set("animationDuration", 300);
        this.rule("MapChart").set("animationDuration", 1000);
        this.rule("MapChart").set("wheelDuration", 300);
        this.rule("Entity").setAll({
            stateAnimationDuration: 600
        });
        this.rule("Sprite").states.create("default", { stateAnimationDuration: 600 });
        this.rule("Tooltip", ["axis"]).setAll({
            animationDuration: 200
        });
        this.rule("WordCloud").set("animationDuration", 500);
        this.rule("Polygon").set("animationDuration", 600);
        this.rule("ArcDiagram").set("animationDuration", 600);
    }
}
//# sourceMappingURL=AnimatedTheme.js.map

/***/ }),

/***/ "./node_modules/@amcharts/amcharts5/themes/Animated.js":
/*!*************************************************************!*\
  !*** ./node_modules/@amcharts/amcharts5/themes/Animated.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _internal_themes_AnimatedTheme__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../.internal/themes/AnimatedTheme */ "./node_modules/@amcharts/amcharts5/.internal/themes/AnimatedTheme.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_internal_themes_AnimatedTheme__WEBPACK_IMPORTED_MODULE_0__.AnimatedTheme);
//# sourceMappingURL=Animated.js.map

/***/ })

}]);